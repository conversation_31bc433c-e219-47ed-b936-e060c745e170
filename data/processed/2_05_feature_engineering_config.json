{"notebook": "2_feature_engineering", "date_processing": "2025-06-03 18:23:59", "reference_date": "2018-10-18", "clustering_features": ["recency", "frequency", "monetary", "customer_lifespan_days", "days_since_first_order", "recency_days", "total_amount", "amount_std", "avg_order_value", "amount_cv", "amount_total", "avg_amount", "amount_std_dev", "min_amount", "max_amount", "amount_cv_coef", "amount_range", "order_value_mean", "total_orders", "purchase_frequency", "order_count", "mon<PERSON>_moyen"], "original_shape": [99441, 28], "final_shape": [99441, 22], "normalization_method": "StandardScaler", "imputation_strategy": "median", "n_customers": 99441, "n_features": 22, "feature_descriptions": {"recency": "Jours depuis le dernier achat", "frequency": "Nombre total d'achats", "monetary": "Montant des achats", "customer_lifespan_days": "Durée entre premier et dernier achat", "days_since_first_order": "Jours depuis le premier achat", "avg_days_between_orders": "<PERSON><PERSON><PERSON> entre commandes", "order_std": "Écart-type des montants", "order_cv": "Coefficient de variation des montants", "monetary_per_frequency": "Montant total / fréquence", "frequency_per_day": "Fréquence normalisée par jour"}, "files_exported": {"clustering_data": "2_01_features_scaled_clustering.csv", "data_with_ids": "2_02_features_scaled_with_ids.csv", "complete_data": "2_03_rfm_enriched_complete.csv", "scaler": "2_04_scaler.joblib"}}