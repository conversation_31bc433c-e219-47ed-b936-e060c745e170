{"processing_info": {"date_processing": "2025-06-03 17:58:49", "notebook_version": "1.0", "python_version": "3.13.2"}, "data_transformation": {"original_shape": [99441, 8], "final_shape": [99441, 14]}, "data_quality": {"missing_values_handled": true, "outliers_detected": true, "outliers_removed": false, "outliers_flagged": true}, "feature_engineering": {"rfm_metrics_created": true, "temporal_features_extracted": true, "customer_segmentation_prepared": true}, "exports_created": {"cleaned_dataset_csv": "data/processed/1_01_cleaned_dataset.csv", "cleaned_dataset_pickle": "data/processed/1_01_cleaned_dataset.pkl", "figures_exported": 12}}