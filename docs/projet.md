# Rôle

Accompagner Olist dans leur projet (propose une solution de vente sur les marketplaces en ligne) et sur leur premier cas d’usage Data Science autour de la segmentation client.



# Mission Principale

Fournir aux équipes d’e-commerce une **segmentation des clients** qu’elles pourront utiliser au quotidien pour leurs campagnes de communication.



# Objectif principale 

Fournir à l’équipe Marketing **une description actionable** de votre segmentation et de sa logique sous-jacente pour une utilisation optimale, ainsi qu’une **proposition de contrat de maintenance** basée sur une analyse de la stabilité des segments au cours du temps.



# Points clés

-   comprendre les différents types d'utilisateurs (utiliser des méthodes non supervisées pour regrouper des clients de profils similaires) - notebook d’analyse exploratoire

-   Dans un deuxième temps, une fois le modèle de segmentation choisi, réaliser une recommandation de fréquence à laquelle la segmentation doit être mise à jour pour rester pertinente, afin de pouvoir effectuer un devis de contrat de maintenance.

    exemple : 

    >   [!IMPORTANT]
    >
    >   ![Schéma RFM, comparant récence, fréquence et montant et classant les clients en groupes.](https://user.oc-static.com/upload/2023/03/21/16793895608947_Screenshot%202023-03-21%20at%2010.05.50.png)



# Les Notebooks :

## Notebook 1 : Analyse exploratoire des données

### Objectif

Nettoyer, explorer et comprendre le jeu de données client pour préparer la segmentation. Inclure analyses univariée, bivariée et multivariée. Utiliser les fonctions utilitaires pour une organisation claire et professionnelle.



### Structure complète du notebook `1_data_exploration.ipynb`

#### 1. Chargement des données

* 1.1 Import des librairies de base (pandas, numpy, matplotlib, seaborn)
* 1.2 Chargement du fichier client e-commerce (transactions, commandes, utilisateurs...)
* 1.3 Aperçu rapide : `df.shape`, `df.dtypes`, `df.head()`
* 1.4 Vérification des doublons et de la clé primaire (ex : customer\_id)

#### 2. Analyse des valeurs manquantes

* 2.1 Affichage du taux de NaN par colonne
* 2.2 Visualisation heatmap des NaN (via Seaborn)
* 2.3 Choix de la stratégie : suppression, imputation moyenne/médiane

#### 3. Analyse univariée

* 3.1 Histogrammes + KDE pour variables numériques (montant d'achat, fréquence, etc.)
* 3.2 Countplots pour variables qualitatives (pays, genre, canal)
* 3.3 Statistiques : moyenne, médiane, skew, kurtosis

#### 4. Analyse bivariée

* 4.1 Analyse de la corrélation entre variables quantitatives (corrélation, scatter)
* 4.2 Boxplots entre montant / fréquence et variables qualitatives
* 4.3 Test de normalité et de linéarité

#### 5. Analyse multivariée

* 5.1 Matrice de corrélation
* 5.2 Pairplot ciblé sur les variables candidates pour la segmentation
* 5.3 Préparation pour l'ACP (optionnel)

#### 6. Détection et traitement des outliers

* 6.1 Détection par Z-score ou IQR
* 6.2 Suppression ou remplacement selon seuil et pertinence
* 6.3 Visualisation post-nettoyage

#### 7. Feature Engineering de base (pré-nettoyage)

* 7.1 Calcul de l'ancienneté client
* 7.2 Taux d'achat, moyenne du panier, jours actifs
* 7.3 Conversion des dates si présentes

#### 8. Sauvegarde du dataset nettoyé

* 8.1 Export CSV ou Pickle du DataFrame nettoyé
* 8.2 Ajout d'un log de version (date, format, shape)

### Résultat attendu

* Un dataset propre, sans outliers majeurs ni valeurs manquantes critiques
* Des visualisations claires pour interpréter les variables
* Des variables préparées pour la phase de Feature Engineering RFM (notebook 2)

**Étapes suivantes :** création des variables de segmentation (Notebook 2)

---

## Notebook 2 : Feature Engineering (RFM & comportements)

###  Objectif

Construire des variables pertinentes pour la segmentation client. Utiliser le modèle RFM (Récence, Fréquence, Montant), compléter avec d’autres variables comportementales, et préparer les données pour le clustering.

### Structure complète du notebook `2_feature_engineering.ipynb`

#### 1. Chargement des données nettoyées

* 1.1 Chargement du fichier nettoyé produit par le Notebook 1
* 1.2 Vérifications d’usage (shape, duplications, valeurs manquantes résiduelles)

#### 2. Calcul des variables RFM

* 2.1 Définir la date de référence pour la récence (ex : max date + 1 jour)
* 2.2 Agrégation par client :

    * R = nombre de jours depuis le dernier achat
    * F = nombre total d’achats
    * M = montant total ou moyen des achats
* 2.3 Construction du DataFrame RFM final

#### 3. Enrichissement comportemental

* 3.1 Ancienneté client
* 3.2 Délai moyen entre commandes
* 3.3 Nombre de catégories achetées
* 3.4 Panier moyen, écart-type, ratio M/F
* 3.5 Taux de retour ou d’annulation (si applicable)

#### 4. Normalisation des variables

* 4.1 Sélection des variables pour clustering (pas de customer\_id !)
* 4.2 Standardisation via `StandardScaler`
* 4.3 Export du DataFrame normalisé (X) + backup des variables d’origine

#### 5. Sauvegarde

* 5.1 Export du jeu final prêt à clusteriser (CSV ou Pickle)
* 5.2 Export de la liste des variables utilisées (JSON ou txt)

### Résultat attendu

* Un jeu de données enrichi, structuré et prêt à être segmenté
* Des variables explicites, justifiées et robustes
* Base prête pour visualisation + clustering (Notebook 3)

---

## Notebook 3 : Clustering & Segmentation

### Objectif

Appliquer des algorithmes de clustering (K-Means, éventuellement DBSCAN ou Agglomératif) sur les données normalisées. Déterminer le nombre optimal de clusters, analyser les profils par segment, et visualiser les résultats.

### Structure complète du notebook `3_clustering_segmentation.ipynb`

#### 1. Chargement des données préparées

* 1.1 Import du jeu de données normalisé
* 1.2 Rappel des variables utilisées pour segmentation (vérif rapide)

#### 2. Recherche du nombre optimal de clusters

* 2.1 Méthode du coude (inertie vs k)
* 2.2 Score de silhouette
* 2.3 Visualisation conjointe des métriques (choix justifié de k)

#### 3. Clustering K-Means

* 3.1 Entraînement de KMeans avec k optimal
* 3.2 Récupération des labels
* 3.3 Sauvegarde du modèle entraîné (optionnel)

#### 4. Réduction de dimension pour visualisation

* 4.1 Application de PCA (2D ou 3D)
* 4.2 Visualisation des clusters (scatterplot coloré)

#### 5. Analyse des clusters

* 5.1 Agrégation des indicateurs par cluster (moyennes, médianes)
* 5.2 Tableaux de synthèse et graphiques comparatifs
* 5.3 Identification des profils types (ex. : acheteur fréquent à fort panier, client inactif...)

#### 6. Sauvegarde

* 6.1 Export du DataFrame labellisé avec les clusters
* 6.2 Export des résumés/statistiques par segment

### Résultat attendu

* Segmentation claire, visuelle et justifiée du portefeuille clients
* Interprétation simple et exploitable des profils par segment
* Base prête pour recommandations marketing (Notebook 4)

---

## Notebook 4 : Analyse des segments & Recommandations marketing

### Objectif

Analyser les segments obtenus via le clustering, identifier des profils clients clairs et formuler des recommandations concrètes et personnalisées pour chaque groupe.

### Structure complète du notebook `4_analysis_recommendations.ipynb`

#### 1. Chargement des données clusterisées

* 1.1 Chargement du DataFrame enrichi avec labels de clusters
* 1.2 Vérification du nombre et répartition des clusters

#### 2. Analyse descriptive des clusters

* 2.1 Moyennes, médianes, stats descriptives par cluster
* 2.2 Comparaison des segments via graphiques (barplots, boxplots, radar charts)
* 2.3 Identification des patterns comportementaux propres à chaque segment

#### 3. Définition des personas clients

* 3.1 Création de fiches profils clients types (1 par cluster)
* 3.2 Association de chaque segment à un comportement d'achat représentatif

#### 4. Recommandations marketing

* 4.1 Déclinaison des leviers marketing selon les clusters :
    * Fidélisation, relance inactifs, up-sell, cross-sell
    * Canal de communication privilégié (email, app, pub...)
    * Contenu personnalisé selon appétences détectées
* 4.2 Mise en forme des recommandations (tableau synthétique, carte des actions)

#### 5. Temporalité & limites

* 5.1 Analyse de la stabilité des clusters dans le temps
* 5.2 Limites de la segmentation : bruit, évolutivité, biais de données

#### 6. Préparation de la présentation

* 6.1 Export des visuels pertinents pour le livrable PowerPoint
* 6.2 Tableaux de synthèse formatés

### Résultat attendu

* Une interprétation claire et business des segments clients
* Des recommandations marketing actionnables et argumentées
* Des livrables prêts à intégrer dans une présentation finale

----

## Notebook 5 : Projet de maintenance et simulation

Bilan de l’ensemble des l’analyses réalisés avec proposition de contrat de maintenance.