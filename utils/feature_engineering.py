"""utils/feature_engineering.py
Module regroupant la logique métier de feature engineering initialement présente dans le notebook
02_feature_engineering :

1. create_temporal_features         – Features temporelles avancées (lifespan, fréquence, saisonnalité)
2. create_satisfaction_features     – Features de satisfaction client et qualité
3. create_advanced_transactional_features – Features transactionnelles avancées
4. create_geographic_features       – Features géographiques et livraison
5. consolidate_all_features         – Fusion intelligente de toutes les features
6. prepare_clustering_features      – Sélection et normalisation pour clustering

Chaque fonction est documentée, typée et optimisée pour être réutilisée dans d'autres
contextes ou modules du projet.
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd

# ---------------------------------------------------------------------------
# Gestion de la dépendance NumPy : fallback minimal si la lib n'est pas installée
# ---------------------------------------------------------------------------
try:
    import numpy as np  # type: ignore
except ModuleNotFoundError:  # Environnements restreints
    import math

    class _NumpyFallback:  # pragma: no cover
        """Fallback minimal pour couvrir les appels numpy courants utilisés ici."""

        number = (int, float)

        @staticmethod
        def median(arr):
            arr = [x for x in arr if x is not None]
            arr.sort()
            n = len(arr)
            if n == 0:
                return 0
            return arr[n // 2] if n % 2 == 1 else (arr[n // 2 - 1] + arr[n // 2]) / 2

        @staticmethod
        def where(condition, x, y):
            """Equivalent basique de np.where."""
            if hasattr(condition, "__iter__"):
                return [x if c else y for c in condition]
            return x if condition else y

        @staticmethod
        def inf():
            return float("inf")

        @staticmethod
        def isinf(x):
            if hasattr(x, "__iter__"):
                return [math.isinf(val) for val in x]
            return math.isinf(x)

        @property  # type: ignore[override]
        def nan(self):  # noqa: D401
            """Retourne NaN (attribut pour compatibilité avec numpy)."""
            return float("nan")

        def sqrt(self, x):
            """Racine carrée basique."""
            if hasattr(x, "__iter__"):
                return [math.sqrt(val) for val in x]
            return math.sqrt(x)

    np = _NumpyFallback()

# Gestion de sklearn avec fallback
try:
    from sklearn.preprocessing import StandardScaler  # type: ignore
except ModuleNotFoundError:

    class StandardScaler:  # pragma: no cover
        """Fallback pour StandardScaler si sklearn non disponible."""

        def __init__(self):
            self.mean_ = None
            self.scale_ = None

        def fit_transform(self, X):
            # Normalisation basique
            means = [sum(col) / len(col) for col in X.T]
            stds = [
                (sum((x - mean) ** 2 for x in col) / len(col)) ** 0.5
                for col, mean in zip(X.T, means)
            ]
            self.mean_ = means
            self.scale_ = stds

            return [
                [
                    (x - mean) / std if std > 0 else 0
                    for x, mean, std in zip(row, means, stds)
                ]
                for row in X
            ]


# ---------------------------------------------------------------------------
# Logging configuration
# ---------------------------------------------------------------------------
logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------------
# 1. FEATURES TEMPORELLES AVANCÉES
# ---------------------------------------------------------------------------


def create_temporal_features(
    orders_df: pd.DataFrame, customer_payments_df: Optional[pd.DataFrame] = None
) -> pd.DataFrame:
    """
    Crée des features temporelles avancées à partir des données de commandes.

    Args:
        orders_df: DataFrame des commandes avec colonnes order_purchase_timestamp, customer_id
        customer_payments_df: DataFrame optionnel des paiements (non utilisé actuellement)

    Returns:
        DataFrame avec features temporelles par customer_id
    """
    if orders_df.empty:
        logger.warning("DataFrame orders vide - retour d'un DataFrame vide")
        return pd.DataFrame(columns=["customer_id"])

    try:
        # S'assurer du format datetime
        orders_df = orders_df.copy()
        orders_df["order_purchase_timestamp"] = pd.to_datetime(
            orders_df["order_purchase_timestamp"]
        )

        # Features temporelles par client
        temporal_features = (
            orders_df.groupby("customer_id")
            .agg(
                {
                    "order_purchase_timestamp": [
                        "min",  # Premier achat
                        "max",  # Dernier achat
                        "count",  # Nombre total de commandes
                    ]
                }
            )
            .reset_index()
        )

        # Aplatir les colonnes multi-niveaux
        temporal_features.columns = [
            "customer_id",
            "first_purchase_date",
            "last_purchase_date",
            "total_orders",
        ]

        # Calculs additionnels
        temporal_features["customer_lifespan_days"] = (
            temporal_features["last_purchase_date"]
            - temporal_features["first_purchase_date"]
        ).dt.days

        # Éviter division par zéro
        temporal_features["customer_lifespan_days"] = temporal_features[
            "customer_lifespan_days"
        ].fillna(0)

        # Fréquence d'achat moyenne (commandes par jour actif)
        temporal_features["avg_order_frequency"] = np.where(
            temporal_features["customer_lifespan_days"] > 0,
            temporal_features["total_orders"]
            / temporal_features["customer_lifespan_days"],
            temporal_features[
                "total_orders"
            ],  # Si un seul jour, garder le nombre total
        )

        # Features saisonnières si données suffisantes
        if len(orders_df) > 0:
            orders_df["purchase_month"] = orders_df["order_purchase_timestamp"].dt.month
            orders_df["purchase_dayofweek"] = orders_df[
                "order_purchase_timestamp"
            ].dt.dayofweek
            orders_df["purchase_hour"] = orders_df["order_purchase_timestamp"].dt.hour

            # Diversité temporelle (nombre de mois différents d'achat)
            month_diversity = (
                orders_df.groupby("customer_id")["purchase_month"]
                .nunique()
                .reset_index()
            )
            month_diversity.columns = ["customer_id", "purchase_month_diversity"]

            temporal_features = temporal_features.merge(
                month_diversity, on="customer_id", how="left"
            )

            # Répartition jour semaine/weekend
            orders_df["is_weekend"] = orders_df["purchase_dayofweek"].isin([5, 6])
            weekend_ratio = (
                orders_df.groupby("customer_id")["is_weekend"].mean().reset_index()
            )
            weekend_ratio.columns = ["customer_id", "weekend_purchase_ratio"]

            temporal_features = temporal_features.merge(
                weekend_ratio, on="customer_id", how="left"
            )

        # Remplir les valeurs manquantes
        for col in temporal_features.select_dtypes(include=["number"]).columns:
            temporal_features[col] = temporal_features[col].fillna(0)

        logger.info(
            f"Features temporelles créées pour {len(temporal_features):,} clients"
        )
        return temporal_features

    except Exception as e:
        logger.error(f"Erreur création features temporelles: {e}")
        return pd.DataFrame(columns=["customer_id"])


# ---------------------------------------------------------------------------
# 2. FEATURES DE SATISFACTION CLIENT
# ---------------------------------------------------------------------------


def create_satisfaction_features(
    reviews_df: pd.DataFrame, orders_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Crée des features liées à la satisfaction client à partir des reviews.

    Args:
        reviews_df: DataFrame des reviews avec colonnes order_id, review_score
        orders_df: DataFrame des commandes avec colonnes order_id, customer_id

    Returns:
        DataFrame avec features de satisfaction par customer_id
    """
    if reviews_df.empty:
        logger.warning("DataFrame reviews vide - retour d'un DataFrame vide")
        return pd.DataFrame(columns=["customer_id"])

    try:
        # Joindre reviews avec orders pour avoir customer_id
        if not orders_df.empty and "customer_id" in orders_df.columns:
            reviews_with_customers = reviews_df.merge(
                orders_df[["order_id", "customer_id"]], on="order_id", how="inner"
            )
        else:
            # Fallback si pas de données orders
            logger.warning("Pas de données orders disponibles")
            return pd.DataFrame(columns=["customer_id"])

        if reviews_with_customers.empty:
            logger.warning("Aucune review après jointure")
            return pd.DataFrame(columns=["customer_id"])

        # Features de satisfaction par client
        satisfaction_features = (
            reviews_with_customers.groupby("customer_id")
            .agg({"review_score": ["mean", "std", "count", "min", "max"]})
            .reset_index()
        )

        # Aplatir les colonnes
        satisfaction_features.columns = [
            "customer_id",
            "avg_review_score",
            "review_score_std",
            "total_reviews",
            "min_review_score",
            "max_review_score",
        ]

        # Features dérivées
        satisfaction_features["review_score_std"] = satisfaction_features[
            "review_score_std"
        ].fillna(0)

        # Consistance des reviews (inverse de l'écart-type)
        satisfaction_features["review_consistency"] = np.where(
            satisfaction_features["review_score_std"] > 0,
            1 / (1 + satisfaction_features["review_score_std"]),
            1.0,
        )

        # Pourcentage de reviews élevées (4-5)
        high_scores = (
            reviews_with_customers[reviews_with_customers["review_score"] >= 4]
            .groupby("customer_id")
            .size()
            .reset_index(name="high_score_count")
        )

        satisfaction_features = satisfaction_features.merge(
            high_scores, on="customer_id", how="left"
        )
        satisfaction_features["high_score_count"] = satisfaction_features[
            "high_score_count"
        ].fillna(0)

        satisfaction_features["high_score_ratio"] = (
            satisfaction_features["high_score_count"]
            / satisfaction_features["total_reviews"]
        ).fillna(0)

        # Statut satisfaction
        satisfaction_features["satisfaction_status"] = np.where(
            satisfaction_features["avg_review_score"] >= 4,
            "Satisfait",
            np.where(
                satisfaction_features["avg_review_score"] >= 3, "Neutre", "Insatisfait"
            ),
        )

        logger.info(
            f"Features satisfaction créées pour {len(satisfaction_features):,} clients"
        )
        return satisfaction_features

    except Exception as e:
        logger.error(f"Erreur création features satisfaction: {e}")
        return pd.DataFrame(columns=["customer_id"])


# ---------------------------------------------------------------------------
# 3. FEATURES TRANSACTIONNELLES AVANCÉES
# ---------------------------------------------------------------------------


def create_advanced_transactional_features(
    payments_df: pd.DataFrame, orders_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Crée des features transactionnelles avancées à partir des paiements.

    Args:
        payments_df: DataFrame des paiements avec colonnes order_id, payment_value, etc.
        orders_df: DataFrame des commandes avec colonnes order_id, customer_id

    Returns:
        DataFrame avec features transactionnelles par customer_id
    """
    if payments_df.empty or orders_df.empty:
        logger.warning("DataFrames payments ou orders vides")
        return pd.DataFrame(columns=["customer_id"])

    try:
        # Joindre paiements avec commandes
        transaction_data = payments_df.merge(
            orders_df[["order_id", "customer_id"]], on="order_id", how="inner"
        )

        if transaction_data.empty:
            logger.warning("Aucune transaction après jointure")
            return pd.DataFrame(columns=["customer_id"])

        # Features transactionnelles par client
        agg_dict = {
            "payment_value": ["mean", "std", "min", "max", "sum", "count"],
        }

        # Ajouter payment_installments si présent
        if "payment_installments" in transaction_data.columns:
            agg_dict["payment_installments"] = ["mean", "max"]

        trans_features = (
            transaction_data.groupby("customer_id").agg(agg_dict).reset_index()
        )

        # Aplatir les colonnes
        base_cols = [
            "customer_id",
            "avg_payment_value",
            "payment_value_std",
            "min_payment_value",
            "max_payment_value",
            "total_payment_value",
            "payment_count",
        ]

        if "payment_installments" in transaction_data.columns:
            base_cols.extend(["avg_installments", "max_installments"])

        trans_features.columns = base_cols

        # Gérer les valeurs manquantes
        for col in trans_features.select_dtypes(include=["number"]).columns:
            if col != "customer_id":
                trans_features[col] = trans_features[col].fillna(0)

        # Features dérivées
        trans_features["payment_consistency"] = np.where(
            trans_features["payment_value_std"] > 0,
            trans_features["avg_payment_value"] / trans_features["payment_value_std"],
            trans_features["avg_payment_value"],
        )

        # Tendance spending (ratio max/min)
        trans_features["spending_range_ratio"] = np.where(
            trans_features["min_payment_value"] > 0,
            trans_features["max_payment_value"] / trans_features["min_payment_value"],
            1.0,
        )

        # Diversité des types de paiement si disponible
        if "payment_type" in transaction_data.columns:
            payment_type_diversity = (
                transaction_data.groupby("customer_id")["payment_type"]
                .nunique()
                .reset_index()
            )
            payment_type_diversity.columns = ["customer_id", "payment_type_diversity"]

            trans_features = trans_features.merge(
                payment_type_diversity, on="customer_id", how="left"
            )
            trans_features["payment_type_diversity"] = trans_features[
                "payment_type_diversity"
            ].fillna(1)

        logger.info(
            f"Features transactionnelles créées pour {len(trans_features):,} clients"
        )
        return trans_features

    except Exception as e:
        logger.error(f"Erreur création features transactionnelles: {e}")
        return pd.DataFrame(columns=["customer_id"])


# ---------------------------------------------------------------------------
# 4. FEATURES GÉOGRAPHIQUES
# ---------------------------------------------------------------------------


def create_geographic_features(
    customers_df: pd.DataFrame, orders_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Crée des features géographiques à partir des données clients.

    Args:
        customers_df: DataFrame des clients avec colonnes customer_id, customer_state, etc.
        orders_df: DataFrame des commandes avec delivery_time_days (optionnel)

    Returns:
        DataFrame avec features géographiques par customer_id
    """
    if customers_df.empty:
        logger.warning("DataFrame customers vide")
        return pd.DataFrame(columns=["customer_id"])

    try:
        geo_features = customers_df[["customer_id"]].copy()

        # Features géographiques de base si disponibles
        if "customer_state" in customers_df.columns:
            # Encoder l'état comme feature
            geo_features["customer_state"] = customers_df["customer_state"]

            # Compter le nombre de clients par état pour features de popularité
            state_counts = customers_df["customer_state"].value_counts()
            geo_features["state_customer_density"] = geo_features["customer_state"].map(
                state_counts
            )

            # Catégoriser les états par densité
            median_density = geo_features["state_customer_density"].median()
            geo_features["state_category"] = np.where(
                geo_features["state_customer_density"] >= median_density,
                "High_Density",
                "Low_Density",
            )

        if "customer_city" in customers_df.columns:
            geo_features["customer_city"] = customers_df["customer_city"]

            # Densité par ville
            city_counts = customers_df["customer_city"].value_counts()
            geo_features["city_customer_density"] = geo_features["customer_city"].map(
                city_counts
            )

        # Features de coordonnées si disponibles
        if all(col in customers_df.columns for col in ["customer_lat", "customer_lng"]):
            geo_features["customer_lat"] = customers_df["customer_lat"]
            geo_features["customer_lng"] = customers_df["customer_lng"]

            # Calculer la distance au centre du pays (approximation)
            center_lat, center_lng = -14.235, -51.925  # Centre du Brésil
            geo_features["distance_to_center"] = np.sqrt(
                (geo_features["customer_lat"] - center_lat) ** 2
                + (geo_features["customer_lng"] - center_lng) ** 2
            )

        # Si on a les données de commandes avec délais de livraison
        if not orders_df.empty and "delivery_time_days" in orders_df.columns:
            # Calculer le délai moyen de livraison par client
            delivery_stats = (
                orders_df.groupby("customer_id")["delivery_time_days"]
                .agg(["mean", "std", "count"])
                .reset_index()
            )
            delivery_stats.columns = [
                "customer_id",
                "avg_delivery_time",
                "delivery_time_std",
                "delivery_count",
            ]

            geo_features = geo_features.merge(
                delivery_stats, on="customer_id", how="left"
            )

            # Remplir les valeurs manquantes
            for col in ["avg_delivery_time", "delivery_time_std", "delivery_count"]:
                if col in geo_features.columns:
                    geo_features[col] = geo_features[col].fillna(0)

        logger.info(f"Features géographiques créées pour {len(geo_features):,} clients")
        return geo_features

    except Exception as e:
        logger.error(f"Erreur création features géographiques: {e}")
        return pd.DataFrame(columns=["customer_id"])


# ---------------------------------------------------------------------------
# 5. CONSOLIDATION DE TOUTES LES FEATURES
# ---------------------------------------------------------------------------


def consolidate_all_features(
    rfm_data: pd.DataFrame,
    temporal_features: pd.DataFrame,
    satisfaction_features: pd.DataFrame,
    transactional_features: pd.DataFrame,
    geographic_features: pd.DataFrame,
) -> pd.DataFrame:
    """
    Consolide toutes les features en un seul DataFrame.

    Args:
        rfm_data: DataFrame RFM de base
        temporal_features: Features temporelles
        satisfaction_features: Features de satisfaction
        transactional_features: Features transactionnelles
        geographic_features: Features géographiques

    Returns:
        DataFrame consolidé avec toutes les features
    """
    # Partir des données RFM de base
    final_features = rfm_data.copy()
    logger.info(f"Base RFM: {len(final_features):,} clients")

    # Fusionner progressivement toutes les features
    if not temporal_features.empty:
        final_features = final_features.merge(
            temporal_features, on="customer_id", how="left"
        )
        logger.info(f"+ Temporelles: {len(temporal_features.columns)-1} features")

    if not satisfaction_features.empty:
        final_features = final_features.merge(
            satisfaction_features, on="customer_id", how="left"
        )
        logger.info(f"+ Satisfaction: {len(satisfaction_features.columns)-1} features")

    if not transactional_features.empty:
        final_features = final_features.merge(
            transactional_features, on="customer_id", how="left"
        )
        logger.info(
            f"+ Transactionnelles: {len(transactional_features.columns)-1} features"
        )

    if not geographic_features.empty:
        final_features = final_features.merge(
            geographic_features, on="customer_id", how="left"
        )
        logger.info(f"+ Géographiques: {len(geographic_features.columns)-1} features")

    logger.info(
        f"Dataset final: {final_features.shape[0]:,} clients × {final_features.shape[1]} features"
    )

    return final_features


# ---------------------------------------------------------------------------
# 6. PRÉPARATION POUR CLUSTERING
# ---------------------------------------------------------------------------


def prepare_clustering_features(
    final_features: pd.DataFrame, exclude_cols: Optional[List[str]] = None
) -> Tuple[pd.DataFrame, StandardScaler, List[str]]:
    """
    Prépare les features pour le clustering avec nettoyage et normalisation.

    Args:
        final_features: DataFrame avec toutes les features
        exclude_cols: Colonnes à exclure du clustering

    Returns:
        Tuple de (DataFrame normalisé, Scaler utilisé, Liste des features sélectionnées)
    """
    if exclude_cols is None:
        exclude_cols = []

    # Identifier les colonnes numériques et catégorielles
    numeric_cols = final_features.select_dtypes(include=["number"]).columns.tolist()

    # Retirer customer_id et colonnes à exclure
    clustering_features = [
        col for col in numeric_cols if col not in ["customer_id"] + exclude_cols
    ]

    # Nettoyer les features
    clustering_data = final_features[["customer_id"] + clustering_features].copy()

    # Traitement des valeurs manquantes
    for col in clustering_features:
        if clustering_data[col].isnull().sum() > 0:
            median_val = clustering_data[col].median()
            clustering_data[col] = clustering_data[col].fillna(median_val)

    # Traitement des valeurs infinies
    for col in clustering_features:
        clustering_data[col] = clustering_data[col].replace([np.inf, -np.inf], np.nan)
        if clustering_data[col].isnull().sum() > 0:
            median_val = clustering_data[col].median()
            clustering_data[col] = clustering_data[col].fillna(median_val)

    # Exclure les features avec trop peu de variance
    low_variance_features = []
    for col in clustering_features:
        if clustering_data[col].std() < 0.01:
            low_variance_features.append(col)

    final_clustering_features = [
        f for f in clustering_features if f not in low_variance_features
    ]

    if low_variance_features:
        logger.warning(f"Features exclues (faible variance): {low_variance_features}")

    # Normalisation avec StandardScaler
    scaler = StandardScaler()
    clustering_data_scaled = clustering_data.copy()
    clustering_data_scaled[final_clustering_features] = scaler.fit_transform(
        clustering_data[final_clustering_features]
    )

    logger.info(f"Features préparées pour clustering: {len(final_clustering_features)}")
    logger.info(f"Dimensions finales: {clustering_data_scaled.shape}")

    return clustering_data_scaled, scaler, final_clustering_features


def clean_and_impute_features(
    df: pd.DataFrame, id_col: str = "customer_id"
) -> Tuple[pd.DataFrame, List[str], List[str]]:
    """
    Nettoie un DataFrame de features :
    - Identifie les colonnes numériques et catégorielles
    - Impute les NaN numériques par la médiane
    - Remplace les valeurs infinies par NaN puis impute
    - Retourne le DataFrame nettoyé, la liste des colonnes numériques et catégorielles
    """
    df_clean = df.copy()
    numeric_cols = df_clean.select_dtypes(include=["number"]).columns.tolist()
    categorical_cols = df_clean.select_dtypes(include=["object"]).columns.tolist()
    if id_col in numeric_cols:
        numeric_cols.remove(id_col)
    # Imputation NaN numériques
    for col in numeric_cols:
        if df_clean[col].isnull().sum() > 0:
            median_val = df_clean[col].median()
            df_clean[col] = df_clean[col].fillna(median_val)
    # Remplacement des infinis
    for col in numeric_cols:
        df_clean[col] = df_clean[col].replace([np.inf, -np.inf], np.nan)
        if df_clean[col].isnull().sum() > 0:
            median_val = df_clean[col].median()
            df_clean[col] = df_clean[col].fillna(median_val)
    return df_clean, numeric_cols, categorical_cols


def analyze_feature_correlation(
    df: pd.DataFrame, cols: List[str], threshold: float = 0.8
) -> List[Tuple[str, str, float]]:
    """
    Analyse la matrice de corrélation et retourne la liste des paires de features fortement corrélées (|r| > threshold).
    """
    corr_matrix = df[cols].corr()
    high_corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i + 1, len(corr_matrix.columns)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > threshold:
                high_corr_pairs.append(
                    (corr_matrix.columns[i], corr_matrix.columns[j], corr_val)
                )
    return high_corr_pairs


def calculate_rfm(
    df: pd.DataFrame,
    customer_id_col: str = "customer_id",
    date_col: str = "order_purchase_timestamp",
    amount_col: str = "total_amount",
    ref_date: Optional[pd.Timestamp] = None,
) -> pd.DataFrame:
    """
    Calcule les indicateurs RFM (Récence, Fréquence, Montant) pour chaque client.

    Args:
        df: DataFrame contenant les transactions (une ligne par commande)
        customer_id_col: Nom de la colonne identifiant client
        date_col: Nom de la colonne date d'achat
        amount_col: Nom de la colonne montant de la commande
        ref_date: Date de référence pour le calcul de la récence (par défaut, max de date_col)

    Returns:
        DataFrame indexé par customer_id avec colonnes ['recence', 'frequence', 'montant']
    """
    if df.empty:
        logger.warning("DataFrame transactions vide - retour d'un DataFrame vide")
        return pd.DataFrame(
            columns=[customer_id_col, "recence", "frequence", "montant"]
        ).set_index(customer_id_col)

    df = df.copy()
    df[date_col] = pd.to_datetime(df[date_col], errors="coerce")
    if ref_date is None:
        ref_date = df[date_col].max()

    def recence_func(x):
        last_date = x.max()
        if pd.notnull(last_date) and pd.notnull(ref_date):
            last_date = pd.Timestamp(last_date).to_pydatetime()
            ref = pd.Timestamp(ref_date).to_pydatetime()
            return (ref - last_date).days
        return None

    rfm = (
        df.groupby(customer_id_col)
        .agg(
            recence=(date_col, recence_func),
            frequence=(date_col, "count"),
            montant=(amount_col, "sum"),
        )
        .reset_index()
        .set_index(customer_id_col)
    )
    logger.info(f"RFM calculé pour {len(rfm):,} clients")
    return rfm
