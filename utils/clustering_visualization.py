"""
Module de visualisation avancée pour le clustering client

Ce module fusionne et optimise toutes les fonctions de visualisation de clustering :
- Graphiques d'optimisation (coude, silhouette)
- Projections 2D (PCA, t-SNE)
- Profils radar, distributions, tailles de clusters
- Dashboards comparatifs
- Gestion centralisée de la mémoire et de la sauvegarde des figures

API centralisée pour une utilisation professionnelle dans les notebooks OPC5.

Exemple d'utilisation :

    from utils.clustering_visualization import (
        plot_elbow_curve, plot_clusters_2d, plot_cluster_profiles,
        plot_cluster_feature_distributions, plot_cluster_comparison,
        plot_cluster_sizes, create_clustering_dashboard,
        VisualizationManager, optimize_clustering_visualizations
    )

"""

import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
import numpy as np
import pandas as pd
import warnings
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Tuple
import gc
from pathlib import Path
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import os

# --- Classe de gestion centralisée ---

class VisualizationManager:
    """
    Gestionnaire central pour optimiser les visualisations dans les notebooks.
    - Gestion automatique de la mémoire matplotlib
    - Sauvegarde automatique des figures importantes
    - Context manager pour figures
    - Visualisation par lot
    """
    def __init__(self, auto_save: bool = True, save_dir: Optional[str] = None,
                 max_figures: int = 10, backend: str = 'Agg'):
        self.auto_save = auto_save
        self.save_dir = Path(save_dir) if save_dir else Path("reports/figures")
        self.max_figures = max_figures
        self.backend = backend
        self.figure_counter = 0
        self.open_figures = []
        self.save_dir.mkdir(parents=True, exist_ok=True)
        plt.switch_backend(backend)
    def __enter__(self):
        return self
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup_all()
    @contextmanager
    def managed_figure(self, figsize: Tuple[int, int] = (10, 6),
                      save_name: Optional[str] = None, show: bool = False):
        if len(plt.get_fignums()) >= self.max_figures:
            self.cleanup_old_figures()
        fig = plt.figure(figsize=figsize)
        self.open_figures.append(fig.number)
        self.figure_counter += 1
        try:
            yield fig
            plt.tight_layout()
            if self.auto_save and save_name:
                self.save_figure(fig, save_name)
            if show:
                plt.show()
        finally:
            if fig.number in self.open_figures:
                self.open_figures.remove(fig.number)
            plt.close(fig)
    def save_figure(self, fig, filename: str, dpi: int = 300,
                   formats: List[str] = ['png']):
        try:
            for fmt in formats:
                filepath = self.save_dir / f"{filename}.{fmt}"
                fig.savefig(filepath, dpi=dpi, bbox_inches='tight',
                           format=fmt, facecolor='white')
            return True
        except Exception as e:
            warnings.warn(f"Erreur sauvegarde figure {filename}: {e}")
            return False
    def cleanup_old_figures(self, keep_last: int = 5):
        all_figs = plt.get_fignums()
        if len(all_figs) > keep_last:
            for fig_num in all_figs[:-keep_last]:
                plt.close(fig_num)
                if fig_num in self.open_figures:
                    self.open_figures.remove(fig_num)
        gc.collect()
    def cleanup_all(self):
        plt.close('all')
        self.open_figures.clear()
        gc.collect()
    def batch_visualize(self, plot_functions: List[callable],
                       save_names: Optional[List[str]] = None,
                       show_final_only: bool = True):
        results = []
        save_names = save_names or [None] * len(plot_functions)
        for i, (plot_func, save_name) in enumerate(zip(plot_functions, save_names)):
            show_current = show_final_only and (i == len(plot_functions) - 1)
            with self.managed_figure(save_name=save_name, show=show_current) as fig:
                result = plot_func()
                results.append(result)
        return results

# --- Fonctions de visualisation de clustering ---

def plot_elbow_curve(
    k_range: range,
    inertias: List[float],
    silhouette_scores: List[float],
    figsize: Tuple[int, int] = (15, 5),
) -> plt.Figure:
    """
    Affiche la courbe du coude et les scores silhouette pour le choix du nombre de clusters.
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
    ax1.plot(k_range, inertias, "bo-", linewidth=2, markersize=8)
    ax1.set_xlabel("Nombre de clusters (k)", fontweight="bold")
    ax1.set_ylabel("Inertie", fontweight="bold")
    ax1.set_title("Méthode du Coude", fontsize=14, fontweight="bold")
    ax1.grid(True, alpha=0.3)
    ax1.set_xticks(k_range)
    ax2.plot(k_range, silhouette_scores, "ro-", linewidth=2, markersize=8)
    ax2.set_xlabel("Nombre de clusters (k)", fontweight="bold")
    ax2.set_ylabel("Score Silhouette", fontweight="bold")
    ax2.set_title("Scores Silhouette", fontsize=14, fontweight="bold")
    ax2.grid(True, alpha=0.3)
    ax2.set_xticks(k_range)
    best_k_idx = np.argmax(silhouette_scores)
    best_k = list(k_range)[best_k_idx]
    best_score = silhouette_scores[best_k_idx]
    ax2.scatter(
        best_k,
        best_score,
        color="red",
        s=100,
        marker="*",
        label=f"Optimal k={best_k} ({best_score:.3f})",
    )
    ax2.legend()
    plt.tight_layout()
    return fig

def plot_clusters_2d(
    data: np.ndarray,
    labels: np.ndarray,
    feature_names: Optional[List[str]] = None,
    method: str = "pca",
    figsize: Tuple[int, int] = (12, 8),
) -> plt.Figure:
    """
    Affiche une projection 2D des clusters (PCA ou t-SNE).
    """
    fig, ax = plt.subplots(1, 1, figsize=figsize)
    if method.lower() == "pca":
        reducer = PCA(n_components=2, random_state=42)
        data_2d = reducer.fit_transform(data)
        xlabel = f"PC1 ({reducer.explained_variance_ratio_[0]:.1%} variance)"
        ylabel = f"PC2 ({reducer.explained_variance_ratio_[1]:.1%} variance)"
        title = "Projection PCA des Clusters"
    elif method.lower() == "tsne":
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        data_2d = reducer.fit_transform(data)
        xlabel = "t-SNE Dimension 1"
        ylabel = "t-SNE Dimension 2"
        title = "Projection t-SNE des Clusters"
    else:
        raise ValueError("method doit être 'pca' ou 'tsne'")
    unique_labels = np.unique(labels)
    colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))
    for i, label in enumerate(unique_labels):
        if label == -1:
            color = "black"
            marker = "x"
            alpha = 0.5
            label_name = "Outliers"
        else:
            color = colors[i % len(colors)]
            marker = "o"
            alpha = 0.7
            label_name = f"Cluster {label}"
        mask = labels == label
        ax.scatter(
            data_2d[mask, 0],
            data_2d[mask, 1],
            c=[color],
            marker=marker,
            alpha=alpha,
            label=label_name,
            s=50,
        )
    ax.set_xlabel(xlabel, fontweight="bold")
    ax.set_ylabel(ylabel, fontweight="bold")
    ax.set_title(title, fontsize=14, fontweight="bold")
    ax.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    return fig

def plot_cluster_profiles(
    data: np.ndarray,
    labels: np.ndarray,
    feature_names: List[str],
    figsize: Tuple[int, int] = (15, 10),
) -> plt.Figure:
    """
    Affiche un graphique radar des profils moyens des clusters.
    """
    df_analysis = pd.DataFrame(data, columns=feature_names)
    df_analysis["cluster"] = labels
    cluster_means = df_analysis.groupby("cluster")[feature_names].mean()
    max_features = 6
    if len(feature_names) > max_features:
        feature_variance = cluster_means.var()
        top_features = feature_variance.nlargest(max_features).index.tolist()
        cluster_means = cluster_means[top_features]
        feature_names = top_features
    cluster_means_norm = (cluster_means - cluster_means.min()) / (
        cluster_means.max() - cluster_means.min()
    )
    cluster_means_norm = cluster_means_norm.fillna(0.5)
    n_clusters = len(cluster_means_norm)
    n_cols = min(3, n_clusters)
    n_rows = (n_clusters - 1) // n_cols + 1
    fig, axes = plt.subplots(n_rows, n_cols, subplot_kw=dict(polar=True), figsize=figsize)
    if n_clusters == 1:
        axes = [axes]
    axes = np.array(axes).flatten()
    angles = np.linspace(0, 2 * np.pi, len(feature_names), endpoint=False).tolist()
    for idx, (cluster, row) in enumerate(cluster_means_norm.iterrows()):
        values = row.tolist()
        values += values[:1]
        ax = axes[idx]
        ax.plot(angles + angles[:1], values, linewidth=2, label=f"Cluster {cluster}")
        ax.fill(angles + angles[:1], values, alpha=0.25)
        ax.set_xticks(angles)
        ax.set_xticklabels(feature_names)
        ax.set_yticklabels([])
        ax.set_title(f"Profil Cluster {cluster}")
    for j in range(idx + 1, len(axes)):
        fig.delaxes(axes[j])
    plt.tight_layout()
    return fig

def plot_cluster_feature_distributions(
    data: np.ndarray,
    labels: np.ndarray,
    feature_names: List[str],
    figsize: Tuple[int, int] = (15, 10),
) -> plt.Figure:
    """
    Affiche la distribution des features pour chaque cluster.
    """
    df = pd.DataFrame(data, columns=feature_names)
    df["cluster"] = labels
    n_features = len(feature_names)
    n_cols = 2
    n_rows = (n_features + 1) // n_cols
    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)
    axes = axes.flatten()
    for i, feature in enumerate(feature_names):
        ax = axes[i]
        for cluster in np.unique(labels):
            sns.kdeplot(
                df[df["cluster"] == cluster][feature],
                ax=ax,
                label=f"Cluster {cluster}",
                fill=True,
                alpha=0.3,
            )
        ax.set_title(feature)
        ax.legend()
    for j in range(i + 1, len(axes)):
        fig.delaxes(axes[j])
    plt.tight_layout()
    return fig

def plot_cluster_sizes(
    labels: np.ndarray, algorithm_name: str = "", figsize: Tuple[int, int] = (10, 6)
) -> plt.Figure:
    """
    Affiche la taille de chaque cluster sous forme d'histogramme.
    """
    unique, counts = np.unique(labels, return_counts=True)
    fig, ax = plt.subplots(figsize=figsize)
    sns.barplot(x=unique, y=counts, ax=ax, palette="Set2")
    ax.set_xlabel("Cluster")
    ax.set_ylabel("Nombre d'observations")
    ax.set_title(f"Taille des clusters {algorithm_name}")
    for i, v in enumerate(counts):
        ax.text(i, v + 0.5, str(v), ha="center", fontweight="bold")
    plt.tight_layout()
    return fig

def plot_cluster_comparison(
    comparison_results: dict, figsize: Tuple[int, int] = (15, 10)
) -> plt.Figure:
    """
    Affiche la comparaison des performances de plusieurs algorithmes de clustering.
    """
    algos = list(comparison_results.keys())
    n_algos = len(algos)
    fig, axes = plt.subplots(1, n_algos, figsize=figsize)
    if n_algos == 1:
        axes = [axes]
    for i, algo in enumerate(algos):
        summary = comparison_results[algo]["summary"]
        sizes = summary["cluster_distribution"]
        ax = axes[i]
        sns.barplot(x=list(sizes.keys()), y=list(sizes.values()), ax=ax, palette="Set2")
        ax.set_title(f"{algo} (k={summary['n_clusters']})")
        ax.set_xlabel("Cluster")
        ax.set_ylabel("Taille")
    plt.tight_layout()
    return fig

def create_clustering_dashboard(
    data: np.ndarray,
    comparison_results: dict,
    feature_names: List[str],
    k_range: range,
    inertias: List[float],
    silhouette_scores: List[float],
    selected_algorithm: str,
) -> plt.Figure:
    """
    Crée un dashboard synthétique de clustering (optimisation, projection, tailles, profils).
    """
    fig = plt.figure(constrained_layout=True, figsize=(22, 12))
    gs = fig.add_gridspec(2, 3)
    # Coude & silhouette
    ax1 = fig.add_subplot(gs[0, 0])
    plot_elbow_curve(k_range, inertias, silhouette_scores, figsize=(8, 4))
    plt.sca(ax1)
    # Projection 2D
    ax2 = fig.add_subplot(gs[0, 1])
    labels = comparison_results[selected_algorithm]["labels"]
    plot_clusters_2d(data, labels, feature_names, method="pca", figsize=(8, 6))
    plt.sca(ax2)
    # Tailles
    ax3 = fig.add_subplot(gs[0, 2])
    plot_cluster_sizes(labels, selected_algorithm, figsize=(8, 6))
    plt.sca(ax3)
    # Profils
    ax4 = fig.add_subplot(gs[1, :2], polar=True)
    plot_cluster_profiles(data, labels, feature_names, figsize=(10, 8))
    plt.sca(ax4)
    # Distributions
    ax5 = fig.add_subplot(gs[1, 2])
    plot_cluster_feature_distributions(data, labels, feature_names, figsize=(8, 6))
    plt.sca(ax5)
    plt.tight_layout()
    return fig

# --- API centralisée pour visualisation de clustering ---

def optimize_clustering_visualizations(X: np.ndarray, labels: np.ndarray,
                                     feature_names: List[str],
                                     viz_manager: VisualizationManager,
                                     algorithm_name: str = "clustering") -> Dict[str, Any]:
    """
    Génère un ensemble optimisé de visualisations pour le clustering.
    """
    # Exemple d'utilisation des fonctions ci-dessus avec le gestionnaire
    results = {}
    with viz_manager.managed_figure(save_name=f"{algorithm_name}_elbow"):
        results['elbow'] = plot_elbow_curve(range(2, 11), [0]*9, [0]*9)
    with viz_manager.managed_figure(save_name=f"{algorithm_name}_2d"):
        results['2d'] = plot_clusters_2d(X, labels, feature_names)
    # ... (ajouter d'autres visualisations pertinentes)
    return results

# (À compléter avec les autres fonctions de visualisation du clustering)

def export_figure(
    fig: plt.Figure,
    notebook_name: str,
    export_number: int,
    base_name: str,
    ext: str = "png",
    export_type: str = "figures",
    base_reports_dir: str = "reports",
    dpi: int = 300,
    verbose: bool = True,
    **kwargs
) -> str:
    """
    Exporte une figure matplotlib dans le dossier reports/figures avec un nom harmonisé.
    Utilise la convention : <notebook_name>_<export_number>_<base_name>.<ext>
    Exemple : export_figure(fig, "3_clustering_segmentation", 4, "elbow_curve")
    """
    from utils.data_tools import generate_export_filename, get_export_dir
    export_dir = get_export_dir(base_reports_dir, export_type)
    filename = generate_export_filename(notebook_name, export_number, base_name, ext)
    save_path = os.path.join(export_dir, filename)
    fig.savefig(save_path, dpi=dpi, bbox_inches="tight", **kwargs)
    if verbose:
        print(f"✅ Figure exportée : {save_path}")
    return save_path

# Toutes les fonctions de visualisation doivent utiliser export_figure pour l'export harmonisé.
