{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (RFM & comportements)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client. Utiliser le modèle RFM (Récence, Fréquence, Montant), compléter avec d'autres variables comportementales, et préparer les données pour le clustering.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données nettoyées\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/raw\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/clean\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/reports\n", "Le dossier source /Users/<USER>/Developer/OPC/P5/data/clean/features n'existe pas, rien à déplacer.\n", "==================================================\n", "\u001b[1m\u001b[95m🚀 INITIALISATION DU NOTEBOOK\u001b[0m\n", "==================================================\n", "\u001b[94mNotebook: 2_feature_engineering.ipynb\u001b[0m\n", "\u001b[94mGraine aléatoire: 42\u001b[0m\n", "\u001b[94mStyle seaborn: whitegrid\u001b[0m\n", "\u001b[94mTaille des figures: (12, 8)\u001b[0m\n", "Vérification des bibliothèques disponibles:\n", "==================================================\n", "- Python: 3.13.2\n", "- NumPy: 2.2.5\n", "- Pandas: 2.2.3\n", "- Matplotlib: 3.10.1\n", "- Seaborn: 0.13.2\n", "- Scikit-learn: 1.6.1\n", "- Folium: Disponible\n", "- Plotly: 6.0.1\n", "==================================================\n", "\u001b[92mVisualisations cartographiques interactives DISPONIBLES.\u001b[0m\n", "Options d'affichage pandas configurées:\n", "- max_rows: 100\n", "- max_columns: 100\n", "- width: 1000\n", "- precision: 4\n", "\u001b[94mAppel de setup_notebook_env pour configurer les dossiers...\u001b[0m\n", "\u001b[95m\n", "Dossiers d'export configurés par setup_notebook_env:\u001b[0m\n", "\u001b[92m- base_export: None\u001b[0m\n", "\u001b[92m- figures: /Users/<USER>/Developer/OPC/P5/reports/figures\u001b[0m\n", "\u001b[92m- maps: /Users/<USER>/Developer/OPC/P5/reports/maps\u001b[0m\n", "\u001b[92m- models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[94mExport config: Figures: /Users/<USER>/Developer/OPC/P5/reports/figures, Maps: /Users/<USER>/Developer/OPC/P5/reports/maps, Models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[92mSauvegarde automatique des figures activée (écraser existants: False).\u001b[0m\n", "\n", "==================================================\n", "\n", "\n", "📁 Répertoire de travail : /Users/<USER>/Developer/OPC/P5\n", "📊 Répertoire des rapports : /Users/<USER>/Developer/OPC/P5/reports\n", "🎲 Graine aléatoire : 42\n"]}], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime, timedelta\n", "\n", "# Preprocessing spécifique\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.feature_engineering import (\n", "    calculate_rfm,\n", "    create_temporal_features,\n", "    create_advanced_transactional_features,\n", "    consolidate_all_features,\n", "    prepare_clustering_features\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"2_feature_engineering.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier nettoyé produit par le Notebook 1"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Dataset chargé depuis : data/processed/1_01_cleaned_dataset.csv\n", "📊 Shape : (99441, 14)\n", "📋 Colonnes : ['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "🔍 Vérifications :\n", "- Valeurs manquantes : 2\n", "- Doublons : 0\n", "- Période des données : du 2016-09-04 21:15:19 au 2018-10-17 17:30:18\n"]}], "source": ["# Chargement des données nettoyées du Notebook 1\n", "# Le fichier a été sauvegardé avec la convention de nommage du projet\n", "data_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "\n", "# Vérification de l'existence du fichier\n", "if not os.path.exists(data_path):\n", "    print(f\"⚠️ Fichier non trouvé : {data_path}\")\n", "    print(\"Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.\")\n", "else:\n", "    # Chargement du dataset nettoyé\n", "    df_clean = pd.read_csv(data_path)\n", "    print(f\"✅ Dataset chargé depuis : {data_path}\")\n", "    print(f\"📊 Shape : {df_clean.shape}\")\n", "    print(f\"📋 Colonnes : {list(df_clean.columns)}\")\n", "\n", "    # Vérification de l'intégrité\n", "    print(f\"\\n🔍 Vérifications :\")\n", "    print(f\"- Valeurs manquantes : {df_clean.isnull().sum().sum()}\")\n", "    print(f\"- Doublons : {df_clean.duplicated().sum()}\")\n", "\n", "    # Vérification de la période des données avec le bon nom de colonne\n", "    # Utilisation de 'first_order_date' qui devrait être disponible d'après le code précédent\n", "    if 'first_order_date' in df_clean.columns:\n", "        print(f\"- Période des données : du {df_clean['first_order_date'].min()} au {df_clean['first_order_date'].max()}\")\n", "    else:\n", "        print(\"⚠️ Colonne de date non trouvée. Colonnes disponibles :\")\n", "        print(df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérifications d'usage"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Toutes les métriques RFM sont présentes\n", "\n", "📋 Aperçu des données :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>frequency</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>recency</th>\n", "      <th>customer_state</th>\n", "      <th>customer_city</th>\n", "      <th>monetary</th>\n", "      <th>value_segment</th>\n", "      <th>frequency_outlier</th>\n", "      <th>recency_outlier</th>\n", "      <th>monetary_outlier</th>\n", "      <th>customer_age_days</th>\n", "      <th>customer_age_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>1</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>337</td>\n", "      <td>SP</td>\n", "      <td>osasco</td>\n", "      <td>89.8000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>337</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>1</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>458</td>\n", "      <td>MG</td>\n", "      <td>itapecerica</td>\n", "      <td>54.9000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>458</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>1</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>596</td>\n", "      <td>ES</td>\n", "      <td>nova venecia</td>\n", "      <td>179.9900</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>596</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>1</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>427</td>\n", "      <td>MG</td>\n", "      <td>mendonca</td>\n", "      <td>149.9000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>427</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>1</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>198</td>\n", "      <td>SP</td>\n", "      <td>sao paulo</td>\n", "      <td>93.0000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>198</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  frequency     first_order_date      last_order_date  recency customer_state customer_city  monetary value_segment  frequency_outlier  recency_outlier  monetary_outlier  customer_age_days customer_age_category\n", "0  00012a2ce6f8dcda20d059ce98491703          1  2017-11-14 16:08:26  2017-11-14 16:08:26      337             SP        osasco   89.8000        Faible              False            False             False                337     Ancien (180-365j)\n", "1  000161a058600d5901f007fab4c27140          1  2017-07-16 09:40:32  2017-07-16 09:40:32      458             MG   itapecerica   54.9000        Faible              False            False             False                458   Très ancien (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079          1  2017-02-28 11:06:43  2017-02-28 11:06:43      596             ES  nova venecia  179.9900        Faible              False            False             False                596   Très ancien (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5          1  2017-08-16 13:09:20  2017-08-16 13:09:20      427             MG      mendonca  149.9000        Faible              False            False             False                427   Très ancien (>365j)\n", "4  000379cdec625522490c315e70c7a9fb          1  2018-04-02 13:42:17  2018-04-02 13:42:17      198             SP     sao paulo   93.0000        Faible              False            False             False                198     Ancien (180-365j)"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Statistiques descriptives des métriques RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frequency</th>\n", "      <th>recency</th>\n", "      <th>monetary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.0000</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       frequency    recency   monetary\n", "count 99441.0000 99441.0000 99441.0000\n", "mean      1.0000   289.9002   137.3577\n", "std       0.0000   153.6673   209.8703\n", "min       1.0000     0.0000     0.8500\n", "25%       1.0000   166.0000    45.9900\n", "50%       1.0000   271.0000    86.9000\n", "75%       1.0000   400.0000   149.9000\n", "max       1.0000   772.0000 13440.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 Distribution des segments :\n"]}, {"data": {"text/plain": ["value_segment\n", "Faible    99431\n", "Moyen         9\n", "Elevé         1\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Vérifications d'usage et préparation pour RFM\n", "if 'df_clean' in locals():\n", "    # Vérification des colonnes RFM déjà calculées\n", "    rfm_columns = ['frequency', 'recency', 'monetary']\n", "    missing_rfm = [col for col in rfm_columns if col not in df_clean.columns]\n", "\n", "    if missing_rfm:\n", "        print(f\"⚠️ Métriques RFM manquantes : {missing_rfm}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")\n", "    else:\n", "        print(\"✅ Toutes les métriques RFM sont présentes\")\n", "\n", "        # Aperçu des données\n", "        print(f\"\\n📋 Aperçu des données :\")\n", "        display(df_clean.head())\n", "\n", "        # Statistiques descriptives des métriques RFM\n", "        print(f\"\\n📊 Statistiques descriptives des métriques RFM :\")\n", "        display(df_clean[rfm_columns].describe())\n", "\n", "        # Vérification des segments\n", "        if 'value_segment' in df_clean.columns:\n", "            print(f\"\\n🎯 Distribution des segments :\")\n", "            segment_dist = df_clean['value_segment'].value_counts()\n", "            display(segment_dist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Calcul des variables RFM\n", "\n", "### 2.1 Définition de la date de référence pour la récence"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 Date de référence pour la récence : 2018-10-18 17:30:18\n", "📊 Période d'analyse : du 2016-09-04 21:15:19 au 2018-10-17 17:30:18\n", "🕰️ Durée totale de la période : 772 jours (2.1 années)\n", "\n", "🔍 Vérification de la récence :\n", "- <PERSON><PERSON><PERSON> moyenne : 289.9 jours\n", "- R<PERSON><PERSON> médiane : 271.0 jours\n", "- Récence min : 0.0 jours\n", "- Récence max : 772.0 jours\n"]}], "source": ["# Définition de la date de référence pour calculer la récence\n", "if 'df_clean' in locals():\n", "    # Conversion des colonnes de dates en datetime\n", "    df_clean['first_order_date'] = pd.to_datetime(df_clean['first_order_date'])\n", "    df_clean['last_order_date'] = pd.to_datetime(df_clean['last_order_date'])\n", "\n", "    # Date de référence = dernière date d'achat + 1 jour\n", "    reference_date = df_clean['last_order_date'].max() + <PERSON><PERSON><PERSON>(days=1)\n", "    print(f\"📅 Date de référence pour la récence : {reference_date}\")\n", "    print(f\"📊 Période d'analyse : du {df_clean['first_order_date'].min()} au {df_clean['last_order_date'].max()}\")\n", "\n", "    # Calcul de la durée totale de la période\n", "    period_duration = (df_clean['last_order_date'].max() - df_clean['first_order_date'].min()).days\n", "    print(f\"🕰️ Durée totale de la période : {period_duration} jours ({period_duration/365:.1f} années)\")\n", "\n", "    # Vérification de la cohérence avec la récence déjà calculée\n", "    if 'recency' in df_clean.columns:\n", "        print(f\"\\n🔍 Vérification de la récence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {df_clean['recency'].mean():.1f} jours\")\n", "        print(f\"- R<PERSON><PERSON> médiane : {df_clean['recency'].median():.1f} jours\")\n", "        print(f\"- Récence min : {df_clean['recency'].min():.1f} jours\")\n", "        print(f\"- Récence max : {df_clean['recency'].max():.1f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Agrégation par client : calcul R, F, M"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Vérification des métriques RFM existantes...\n", "✅ Les métriques RFM sont déjà calculées\n", "📊 Données RFM disponibles pour 99,441 clients\n", "\n", "📋 Aperçu des données RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>337</td>\n", "      <td>1</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>458</td>\n", "      <td>1</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>596</td>\n", "      <td>1</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>427</td>\n", "      <td>1</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>198</td>\n", "      <td>1</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  recency  frequency  monetary  montant_moyen\n", "0  00012a2ce6f8dcda20d059ce98491703      337          1   89.8000        89.8000\n", "1  000161a058600d5901f007fab4c27140      458          1   54.9000        54.9000\n", "2  0001fd6190edaaf884bcaf3d49edf079      596          1  179.9900       179.9900\n", "3  0002414f95344307404f0ace7a26f1d5      427          1  149.9000       149.9000\n", "4  000379cdec625522490c315e70c7a9fb      198          1   93.0000        93.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency   monetary  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000     99441.0000\n", "mean    289.9002     1.0000   137.3577       137.3577\n", "std     153.6673     0.0000   209.8703       209.8703\n", "min       0.0000     1.0000     0.8500         0.8500\n", "25%     166.0000     1.0000    45.9900        45.9900\n", "50%     271.0000     1.0000    86.9000        86.9000\n", "75%     400.0000     1.0000   149.9000       149.9000\n", "max     772.0000     1.0000 13440.0000     13440.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Vérification de la cohérence :\n", "- <PERSON><PERSON><PERSON> moyenne : 289.9 jours\n", "- <PERSON><PERSON><PERSON> m<PERSON> : 1.0 commandes\n", "- <PERSON><PERSON> moyen : 137.36 €\n"]}], "source": ["# Calcul des métriques RFM par client\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Vérification des métriques RFM existantes...\")\n", "\n", "    # Vérification des colonnes RFM existantes\n", "    rfm_columns = ['recency', 'frequency', 'monetary']\n", "    if all(col in df_clean.columns for col in rfm_columns):\n", "        print(\"✅ Les métriques RFM sont déjà calculées\")\n", "\n", "        # Création d'un DataFrame RFM à partir des métriques existantes\n", "        rfm_df = df_clean[['customer_id'] + rfm_columns].copy()\n", "\n", "        # Ajout des montants moyens si disponibles\n", "        if 'monetary' in df_clean.columns:\n", "            rfm_df['montant_moyen'] = df_clean['monetary'] / df_clean['frequency']\n", "\n", "        print(f\"📊 Données RFM disponibles pour {len(rfm_df):,} clients\")\n", "        print(f\"\\n📋 Aperçu des données RFM :\")\n", "        display(rfm_df.head())\n", "        print(f\"\\n📈 Statistiques descriptives RFM :\")\n", "        display(rfm_df.describe())\n", "\n", "        # Vérification de la cohérence des métriques\n", "        print(f\"\\n🔍 Vérification de la cohérence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['recency'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['frequency'].mean():.1f} commandes\")\n", "        print(f\"- <PERSON><PERSON> moyen : {rfm_df['monetary'].mean():.2f} €\")\n", "    else:\n", "        print(\"⚠️ Métriques RFM incomplètes. Colonnes manquantes :\")\n", "        missing_cols = [col for col in rfm_columns if col not in df_clean.columns]\n", "        print(f\"- {missing_cols}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Construction et validation du DataFrame RFM final"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Visualisation des distributions RFM...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/y_/cgv899p56slcpfy0rh4wrfkc0000gn/T/ipykernel_62499/471334389.py:49: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  axes[1,1].boxplot([rfm_numeric[col].dropna() for col in rfm_numeric.columns if col != 'customer_id'],\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Figure exportée : reports/figures/2_01_rfm_distributions.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualisation des distributions RFM\n", "if 'rfm_df' in locals():\n", "    print(\"📊 Visualisation des distributions RFM...\")\n", "\n", "    # D<PERSON>terminer les noms de colonnes selon la source (utilitaire ou manuel)\n", "    if 'recence' in rfm_df.columns:\n", "        # Version manuelle\n", "        recency_col, frequency_col = 'recence', 'frequence'\n", "        monetary_total_col, monetary_avg_col = 'montant_total', 'montant_moyen'\n", "    else:\n", "        # Version utilitaire\n", "        recency_col, frequency_col = 'recency', 'frequency'\n", "        monetary_total_col, monetary_avg_col = 'monetary', 'monetary'\n", "\n", "    # C<PERSON>ation de la figure\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('Distribution des Variables RFM', fontsize=16, fontweight='bold')\n", "\n", "    # Récence\n", "    axes[0,0].hist(rfm_df[recency_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0,0].set_title('Distribution de la Récence (jours)')\n", "    axes[0,0].set_xlabel('Jours depuis dernier achat')\n", "    axes[0,0].set_ylabel('Nombre de clients')\n", "    axes[0,0].grid(True, alpha=0.3)\n", "\n", "    # Fréquence\n", "    axes[0,1].hist(rfm_df[frequency_col], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    axes[0,1].set_title('Distribution de la Fréquence')\n", "    axes[0,1].set_xlabel('Nombre d\\'achats')\n", "    axes[0,1].set_ylabel('Nombre de clients')\n", "    axes[0,1].grid(True, alpha=0.3)\n", "\n", "    # Montant (utiliser la colonne appropriée)\n", "    if monetary_total_col in rfm_df.columns:\n", "        monetary_col = monetary_total_col\n", "        title_suffix = 'Total'\n", "    else:\n", "        monetary_col = monetary_avg_col\n", "        title_suffix = 'Moyen'\n", "\n", "    axes[1,0].hist(rfm_df[monetary_col], bins=50, alpha=0.7, color='salmon', edgecolor='black')\n", "    axes[1,0].set_title(f'Distribution du Montant {title_suffix}')\n", "    axes[1,0].set_xlabel('Montant (€)')\n", "    axes[1,0].set_ylabel('Nombre de clients')\n", "    axes[1,0].grid(True, alpha=0.3)\n", "\n", "    # Boxplot des variables RFM pour détecter les outliers\n", "    rfm_numeric = rfm_df.select_dtypes(include=[np.number])\n", "    axes[1,1].boxplot([rfm_numeric[col].dropna() for col in rfm_numeric.columns if col != 'customer_id'],\n", "                      labels=[col for col in rfm_numeric.columns if col != 'customer_id'])\n", "    axes[1,1].set_title('Boxplots des Variables RFM')\n", "    axes[1,1].set_ylabel('Valeurs (normalisées)')\n", "    axes[1,1].tick_params(axis='x', rotation=45)\n", "    axes[1,1].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=1, base_name=\"rfm_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Enrichissement comportemental\n", "\n", "### 3.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des features temporelles...\n", "🔄 Calcul des features temporelles...\n", "📊 Features temporelles calculées pour 99,441 clients\n", "\n", "📈 Statistiques d'ancienneté :\n", "- Ancienneté moyenne : 0.0 jours\n", "- Ancienneté médiane : 0.0 jours\n", "- Ancienneté max : 0.0 jours\n", "\n", "📊 Distribution des catégories d'ancienneté :\n", "- Ancien (180-365j): 41161 clients (41.4%)\n", "- Très ancien (>365j): 30096 clients (30.3%)\n", "- Etabli (90-180j): 18655 clients (18.8%)\n", "- <PERSON><PERSON><PERSON> (30-90j): 9521 clients (9.6%)\n", "- Nouveau (<30j): 8 clients (0.0%)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>customer_age_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>0</td>\n", "      <td>338</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>0</td>\n", "      <td>459</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>0</td>\n", "      <td>597</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>0</td>\n", "      <td>428</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>0</td>\n", "      <td>199</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id    first_order_date     last_order_date  customer_lifespan_days  days_since_first_order customer_age_category\n", "0  00012a2ce6f8dcda20d059ce98491703 2017-11-14 16:08:26 2017-11-14 16:08:26                       0                     338     Ancien (180-365j)\n", "1  000161a058600d5901f007fab4c27140 2017-07-16 09:40:32 2017-07-16 09:40:32                       0                     459   Très ancien (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079 2017-02-28 11:06:43 2017-02-28 11:06:43                       0                     597   Très ancien (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5 2017-08-16 13:09:20 2017-08-16 13:09:20                       0                     428   Très ancien (>365j)\n", "4  000379cdec625522490c315e70c7a9fb 2018-04-02 13:42:17 2018-04-02 13:42:17                       0                     199     Ancien (180-365j)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul de l'ancienneté client et features temporelles\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul manuel des features temporelles\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul de l'ancienneté client\n", "    customer_lifetime = df_clean.groupby('customer_id').agg({\n", "        'first_order_date': 'min',\n", "        'last_order_date': 'max'\n", "    })\n", "\n", "    # Ancienneté = différence entre première et dernière commande\n", "    customer_lifetime['customer_lifespan_days'] = (\n", "        customer_lifetime['last_order_date'] - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ancienneté depuis la première commande\n", "    customer_lifetime['days_since_first_order'] = (\n", "        reference_date - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ajout de catégories d'ancienneté\n", "    customer_lifetime['customer_age_category'] = pd.cut(\n", "        customer_lifetime['days_since_first_order'],\n", "        bins=[0, 30, 90, 180, 365, float('inf')],\n", "        labels=['Nouveau (<30j)', '<PERSON><PERSON><PERSON> (30-90j)', '<PERSON><PERSON><PERSON><PERSON> (90-180j)',\n", "               'Ancien (180-365j)', 'Très ancien (>365j)']\n", "    )\n", "\n", "    temporal_features = customer_lifetime.reset_index()\n", "\n", "    print(f\"📊 Features temporelles calculées pour {len(temporal_features):,} clients\")\n", "    print(f\"\\n📈 Statistiques d'ancienneté :\")\n", "    print(f\"- Ancienneté moyenne : {temporal_features['customer_lifespan_days'].mean():.1f} jours\")\n", "    print(f\"- Ancienneté médiane : {temporal_features['customer_lifespan_days'].median():.1f} jours\")\n", "    print(f\"- Ancienneté max : {temporal_features['customer_lifespan_days'].max():.1f} jours\")\n", "\n", "    # Distribution des catégories d'ancienneté\n", "    print(f\"\\n📊 Distribution des catégories d'ancienneté :\")\n", "    age_distribution = temporal_features['customer_age_category'].value_counts()\n", "    for category, count in age_distribution.items():\n", "        pct = (count / len(temporal_features)) * 100\n", "        print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "    display(temporal_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 <PERSON><PERSON><PERSON> entre commandes"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des features transactionnelles avancées...\n", "📊 Features transactionnelles calculées pour 99,441 clients\n", "\n", "📈 Statistiques des montants :\n", "- Mont<PERSON> moyen par commande : 137.36 €\n", "- Écart-type des montants : nan €\n", "- CV des montants : 0.00\n", "\n", "�� Statistiques de fréquence :\n", "- <PERSON><PERSON><PERSON> d'achat moyenne : inf commandes/jour\n", "- Nombre moyen de commandes : 1.0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>total_orders</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>recency</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>purchase_frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>1</td>\n", "      <td>89.8000</td>\n", "      <td>NaN</td>\n", "      <td>337</td>\n", "      <td>89.8000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>1</td>\n", "      <td>54.9000</td>\n", "      <td>NaN</td>\n", "      <td>458</td>\n", "      <td>54.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>1</td>\n", "      <td>179.9900</td>\n", "      <td>NaN</td>\n", "      <td>596</td>\n", "      <td>179.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>1</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>427</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>1</td>\n", "      <td>93.0000</td>\n", "      <td>NaN</td>\n", "      <td>198</td>\n", "      <td>93.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0051</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  total_orders  total_amount  amount_std  recency  avg_order_value  amount_cv  purchase_frequency\n", "0  00012a2ce6f8dcda20d059ce98491703             1       89.8000         NaN      337          89.8000     0.0000              0.0030\n", "1  000161a058600d5901f007fab4c27140             1       54.9000         NaN      458          54.9000     0.0000              0.0022\n", "2  0001fd6190edaaf884bcaf3d49edf079             1      179.9900         NaN      596         179.9900     0.0000              0.0017\n", "3  0002414f95344307404f0ace7a26f1d5             1      149.9000         NaN      427         149.9000     0.0000              0.0023\n", "4  000379cdec625522490c315e70c7a9fb             1       93.0000         NaN      198          93.0000     0.0000              0.0051"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Vérification des valeurs aberrantes :\n", "- avg_order_value: 7915 valeurs aberrantes (8.0%)\n", "- amount_cv: 0 valeurs aberrantes (0.0%)\n", "- purchase_frequency: 9255 valeurs aberrantes (9.3%)\n"]}], "source": ["# Calcul des features transactionnelles avancées\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des features transactionnelles avancées...\")\n", "\n", "    # Calcul des features à partir des métriques RFM existantes\n", "    transactional_features = df_clean.groupby('customer_id').agg({\n", "        'frequency': 'first',  # <PERSON><PERSON> de commandes\n", "        'monetary': ['first', 'std'],  # Montant total et écart-type\n", "        'recency': 'first'  # <PERSON><PERSON><PERSON> commande\n", "    }).reset_index()\n", "\n", "    # Renommage des colonnes\n", "    transactional_features.columns = ['customer_id', 'total_orders', 'total_amount', 'amount_std', 'recency']\n", "\n", "    # Calcul du montant moyen par commande\n", "    transactional_features['avg_order_value'] = (\n", "        transactional_features['total_amount'] / transactional_features['total_orders']\n", "    )\n", "\n", "    # Calcul du coefficient de variation des montants\n", "    transactional_features['amount_cv'] = (\n", "        transactional_features['amount_std'] / transactional_features['avg_order_value']\n", "    ).<PERSON>na(0)\n", "\n", "    # Cal<PERSON>l de la fréquence d'achat (commandes par jour)\n", "    transactional_features['purchase_frequency'] = (\n", "        transactional_features['total_orders'] / transactional_features['recency']\n", "    ).<PERSON>na(0)\n", "\n", "    print(f\"📊 Features transactionnelles calculées pour {len(transactional_features):,} clients\")\n", "\n", "    print(f\"\\n📈 Statistiques des montants :\")\n", "    print(f\"- <PERSON><PERSON> moyen par commande : {transactional_features['avg_order_value'].mean():.2f} €\")\n", "    print(f\"- Écart-type des montants : {transactional_features['amount_std'].mean():.2f} €\")\n", "    print(f\"- CV des montants : {transactional_features['amount_cv'].mean():.2f}\")\n", "\n", "    print(f\"\\n�� Statistiques de fréquence :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> d'achat moyenne : {transactional_features['purchase_frequency'].mean():.3f} commandes/jour\")\n", "    print(f\"- Nombre moyen de commandes : {transactional_features['total_orders'].mean():.1f}\")\n", "\n", "    # Affichage des résultats\n", "    display(transactional_features.head())\n", "\n", "    # Vérification des valeurs aberrantes\n", "    print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "    for col in ['avg_order_value', 'amount_cv', 'purchase_frequency']:\n", "        q1 = transactional_features[col].quantile(0.25)\n", "        q3 = transactional_features[col].quantile(0.75)\n", "        iqr = q3 - q1\n", "        outliers = transactional_features[\n", "            (transactional_features[col] < q1 - 1.5 * iqr) |\n", "            (transactional_features[col] > q3 + 1.5 * iqr)\n", "        ]\n", "        print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(transactional_features)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Nombre de catégories achetées et diversité"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul de la diversité des achats...\n", "📋 Colonnes disponibles :\n", "['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "📊 Calcul de la variabilité des montants...\n", "\n", "📈 Statistiques des montants :\n", "- Montant total moyen : 137.36 €\n", "- Mont<PERSON> moyen par commande : 137.36 €\n", "- Écart-type moyen : nan €\n", "- CV moyen : 0.000\n", "\n", "🔍 Vérification des valeurs aberrantes :\n", "- amount_std: 0 valeurs aberrantes (0.0%)\n", "- amount_cv: 0 valeurs aberrantes (0.0%)\n", "- amount_range: 0 valeurs aberrantes (0.0%)\n", "\n", "📊 Features calculées pour 99,441 clients\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>total_amount</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>total_orders</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_range</th>\n", "      <th>avg_order_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "      <td>NaN</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>89.8000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "      <td>NaN</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>54.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "      <td>NaN</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>179.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "      <td>NaN</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>93.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  total_amount  avg_amount  amount_std  min_amount  max_amount  total_orders  amount_cv  amount_range  avg_order_value\n", "0  00012a2ce6f8dcda20d059ce98491703       89.8000     89.8000         NaN     89.8000     89.8000             1     0.0000        0.0000          89.8000\n", "1  000161a058600d5901f007fab4c27140       54.9000     54.9000         NaN     54.9000     54.9000             1     0.0000        0.0000          54.9000\n", "2  0001fd6190edaaf884bcaf3d49edf079      179.9900    179.9900         NaN    179.9900    179.9900             1     0.0000        0.0000         179.9900\n", "3  0002414f95344307404f0ace7a26f1d5      149.9000    149.9000         NaN    149.9000    149.9000             1     0.0000        0.0000         149.9000\n", "4  000379cdec625522490c315e70c7a9fb       93.0000     93.0000         NaN     93.0000     93.0000             1     0.0000        0.0000          93.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_amount</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>total_orders</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_range</th>\n", "      <th>avg_order_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       total_amount  avg_amount  amount_std  min_amount  max_amount  total_orders  amount_cv  amount_range  avg_order_value\n", "count    99441.0000  99441.0000      0.0000  99441.0000  99441.0000    99441.0000 99441.0000    99441.0000       99441.0000\n", "mean       137.3577    137.3577         NaN    137.3577    137.3577        1.0000     0.0000        0.0000         137.3577\n", "std        209.8703    209.8703         NaN    209.8703    209.8703        0.0000     0.0000        0.0000         209.8703\n", "min          0.8500      0.8500         NaN      0.8500      0.8500        1.0000     0.0000        0.0000           0.8500\n", "25%         45.9900     45.9900         NaN     45.9900     45.9900        1.0000     0.0000        0.0000          45.9900\n", "50%         86.9000     86.9000         NaN     86.9000     86.9000        1.0000     0.0000        0.0000          86.9000\n", "75%        149.9000    149.9000         NaN    149.9000    149.9000        1.0000     0.0000        0.0000         149.9000\n", "max      13440.0000  13440.0000         NaN  13440.0000  13440.0000        1.0000     0.0000        0.0000       13440.0000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul de la diversité des achats et variabilité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul de la diversité des achats...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    # Création du DataFrame de base avec customer_id\n", "    diversity_features = df_clean[['customer_id']].drop_duplicates().reset_index(drop=True)\n", "\n", "    # Calcul de la variabilité des montants d'achat\n", "    print(\"\\n📊 Calcul de la variabilité des montants...\")\n", "\n", "    # Vérification de la présence des colonnes nécessaires\n", "    if 'monetary' in df_clean.columns and 'frequency' in df_clean.columns:\n", "        # Calcul des statistiques de base\n", "        order_stats = df_clean.groupby('customer_id').agg({\n", "            'monetary': ['sum', 'mean', 'std', 'min', 'max'],\n", "            'frequency': 'first'\n", "        })\n", "\n", "        # Renommage des colonnes\n", "        order_stats.columns = ['total_amount', 'avg_amount', 'amount_std', 'min_amount', 'max_amount', 'total_orders']\n", "        order_stats = order_stats.reset_index()\n", "\n", "        # Calcul des métriques dérivées\n", "        order_stats['amount_cv'] = (order_stats['amount_std'] / order_stats['avg_amount']).fillna(0)\n", "        order_stats['amount_range'] = order_stats['max_amount'] - order_stats['min_amount']\n", "        order_stats['avg_order_value'] = order_stats['total_amount'] / order_stats['total_orders']\n", "\n", "        # Fusion avec le DataFrame de base\n", "        diversity_features = diversity_features.merge(order_stats, on='customer_id', how='left')\n", "\n", "        # Affichage des statistiques\n", "        print(f\"\\n📈 Statistiques des montants :\")\n", "        print(f\"- Montant total moyen : {order_stats['total_amount'].mean():.2f} €\")\n", "        print(f\"- <PERSON><PERSON> moyen par commande : {order_stats['avg_order_value'].mean():.2f} €\")\n", "        print(f\"- Écart-type moyen : {order_stats['amount_std'].mean():.2f} €\")\n", "        print(f\"- CV moyen : {order_stats['amount_cv'].mean():.3f}\")\n", "\n", "        # Vérification des valeurs aberrantes\n", "        print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "        for col in ['amount_std', 'amount_cv', 'amount_range']:\n", "            q1 = order_stats[col].quantile(0.25)\n", "            q3 = order_stats[col].quantile(0.75)\n", "            iqr = q3 - q1\n", "            outliers = order_stats[\n", "                (order_stats[col] < q1 - 1.5 * iqr) |\n", "                (order_stats[col] > q3 + 1.5 * iqr)\n", "            ]\n", "            print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(order_stats)*100:.1f}%)\")\n", "\n", "        # Affichage des résultats\n", "        print(f\"\\n📊 Features calculées pour {len(diversity_features):,} clients\")\n", "        display(diversity_features.head())\n", "\n", "        # Statistiques descriptives\n", "        print(\"\\n📈 Statistiques descriptives :\")\n", "        display(diversity_features.describe())\n", "    else:\n", "        print(\"⚠️ Colonnes 'monetary' ou 'frequency' manquantes\")\n", "        print(\"Colonnes disponibles :\", df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 <PERSON><PERSON> moyen, écart-type et ratios"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Consolidation de toutes les features...\n", "+ 5 features tempo<PERSON><PERSON>\n", "+ 7 features <PERSON><PERSON><PERSON> a<PERSON>\n", "+ 9 features de diversité ajoutées\n", "\n", "🧹 Nettoyage des colonnes en double...\n", "\n", "📊 Dataset enrichi final : (99441, 28)\n", "\n", "📋 Organisation des colonnes :\n", "- Colonnes de base : ['customer_id']\n", "- Métriques RFM : ['recency', 'frequency', 'monetary']\n", "- Features temporelles : ['first_order_date', 'last_order_date', 'customer_lifespan_days', 'days_since_first_order', 'customer_age_category', 'recency_days']\n", "- Features monétaires : ['monetary', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean']\n", "- Features de fréquence : ['frequency', 'total_orders', 'purchase_frequency', 'order_count']\n", "- Autres features : ['montant_moyen']\n", "\n", "📈 Statistiques descriptives :\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/pandas/core/nanops.py:1016: RuntimeWarning: invalid value encountered in subtract\n", "  sqr = _ensure_numeric((avg - values) ** 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>recency_days</th>\n", "      <th>monetary</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_total</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std_dev</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>amount_cv_coef</th>\n", "      <th>amount_range</th>\n", "      <th>order_value_mean</th>\n", "      <th>frequency</th>\n", "      <th>total_orders</th>\n", "      <th>purchase_frequency</th>\n", "      <th>order_count</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441</td>\n", "      <td>99441</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>2017-12-31 08:43:12.776581120</td>\n", "      <td>2017-12-31 08:43:12.776581120</td>\n", "      <td>0.0000</td>\n", "      <td>290.9002</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>2016-09-04 21:15:19</td>\n", "      <td>2016-09-04 21:15:19</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0013</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>2017-09-12 14:46:19</td>\n", "      <td>2017-09-12 14:46:19</td>\n", "      <td>0.0000</td>\n", "      <td>167.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0025</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>2018-01-18 23:04:36</td>\n", "      <td>2018-01-18 23:04:36</td>\n", "      <td>0.0000</td>\n", "      <td>272.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0037</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>2018-05-04 15:42:16</td>\n", "      <td>2018-05-04 15:42:16</td>\n", "      <td>0.0000</td>\n", "      <td>401.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0060</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>2018-10-17 17:30:18</td>\n", "      <td>2018-10-17 17:30:18</td>\n", "      <td>0.0000</td>\n", "      <td>773.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency   monetary               first_order_date                last_order_date  customer_lifespan_days  days_since_first_order  recency_days   monetary  total_amount  amount_std  avg_order_value  amount_cv  amount_total  avg_amount  amount_std_dev  min_amount  max_amount  amount_cv_coef  amount_range  order_value_mean  frequency  total_orders  purchase_frequency  order_count  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000                          99441                          99441              99441.0000              99441.0000    99441.0000 99441.0000    99441.0000      0.0000       99441.0000 99441.0000    99441.0000  99441.0000          0.0000  99441.0000  99441.0000      99441.0000    99441.0000        99441.0000 99441.0000    99441.0000          99441.0000   99441.0000     99441.0000\n", "mean    289.9002     1.0000   137.3577  2017-12-31 08:43:12.776581120  2017-12-31 08:43:12.776581120                  0.0000                290.9002      289.9002   137.3577      137.3577         NaN         137.3577     0.0000      137.3577    137.3577             NaN    137.3577    137.3577          0.0000        0.0000          137.3577     1.0000        1.0000                 inf       1.0000       137.3577\n", "min       0.0000     1.0000     0.8500            2016-09-04 21:15:19            2016-09-04 21:15:19                  0.0000                  1.0000        0.0000     0.8500        0.8500         NaN           0.8500     0.0000        0.8500      0.8500             NaN      0.8500      0.8500          0.0000        0.0000            0.8500     1.0000        1.0000              0.0013       1.0000         0.8500\n", "25%     166.0000     1.0000    45.9900            2017-09-12 14:46:19            2017-09-12 14:46:19                  0.0000                167.0000      166.0000    45.9900       45.9900         NaN          45.9900     0.0000       45.9900     45.9900             NaN     45.9900     45.9900          0.0000        0.0000           45.9900     1.0000        1.0000              0.0025       1.0000        45.9900\n", "50%     271.0000     1.0000    86.9000            2018-01-18 23:04:36            2018-01-18 23:04:36                  0.0000                272.0000      271.0000    86.9000       86.9000         NaN          86.9000     0.0000       86.9000     86.9000             NaN     86.9000     86.9000          0.0000        0.0000           86.9000     1.0000        1.0000              0.0037       1.0000        86.9000\n", "75%     400.0000     1.0000   149.9000            2018-05-04 15:42:16            2018-05-04 15:42:16                  0.0000                401.0000      400.0000   149.9000      149.9000         NaN         149.9000     0.0000      149.9000    149.9000             NaN    149.9000    149.9000          0.0000        0.0000          149.9000     1.0000        1.0000              0.0060       1.0000       149.9000\n", "max     772.0000     1.0000 13440.0000            2018-10-17 17:30:18            2018-10-17 17:30:18                  0.0000                773.0000      772.0000 13440.0000    13440.0000         NaN       13440.0000     0.0000    13440.0000  13440.0000             NaN  13440.0000  13440.0000          0.0000        0.0000        13440.0000     1.0000        1.0000                 inf       1.0000     13440.0000\n", "std     153.6673     0.0000   209.8703                            NaN                            NaN                  0.0000                153.6673      153.6673   209.8703      209.8703         NaN         209.8703     0.0000      209.8703    209.8703             NaN    209.8703    209.8703          0.0000        0.0000          209.8703     0.0000        0.0000                 NaN       0.0000       209.8703"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "⚠️ Valeurs manquantes par colonne :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n"]}], "source": ["# Consolidation de toutes les features\n", "if 'rfm_df' in locals():\n", "    print(\"🔄 Consolidation de toutes les features...\")\n", "\n", "    # Consolidation manuelle\n", "    rfm_enriched = rfm_df.copy()\n", "\n", "    # Fusion avec les features temporelles si disponibles\n", "    if 'temporal_features' in locals() and not temporal_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(temporal_features, on='customer_id', how='left')\n", "        print(f\"+ {len(temporal_features.columns)-1} features temporelles a<PERSON>\")\n", "\n", "    # Fusion avec les features transactionnelles si disponibles\n", "    if 'transactional_features' in locals() and not transactional_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(transactional_features, on='customer_id', how='left')\n", "        print(f\"+ {len(transactional_features.columns)-1} features transactionnelles ajoutées\")\n", "\n", "    # Fusion avec les features de diversité si disponibles\n", "    if 'diversity_features' in locals() and not diversity_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(diversity_features, on='customer_id', how='left')\n", "        print(f\"+ {len(diversity_features.columns)-1} features de diversité ajoutées\")\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "\n", "    # Dictionnaire de mapping pour les colonnes en double\n", "    column_mapping = {\n", "        'recency_x': 'recency',\n", "        'recency_y': 'recency_days',\n", "        'total_orders_x': 'total_orders',\n", "        'total_orders_y': 'order_count',\n", "        'total_amount_x': 'total_amount',\n", "        'total_amount_y': 'amount_total',\n", "        'amount_std_x': 'amount_std',\n", "        'amount_std_y': 'amount_std_dev',\n", "        'amount_cv_x': 'amount_cv',\n", "        'amount_cv_y': 'amount_cv_coef',\n", "        'avg_order_value_x': 'avg_order_value',\n", "        'avg_order_value_y': 'order_value_mean'\n", "    }\n", "\n", "    # Renommage des colonnes\n", "    rfm_enriched = rfm_enriched.rename(columns=column_mapping)\n", "\n", "    # Suppression des colonnes en double\n", "    duplicate_cols = [col for col in rfm_enriched.columns if col.endswith(('_x', '_y'))]\n", "    rfm_enriched = rfm_enriched.drop(columns=duplicate_cols)\n", "\n", "    # Organisation des colonnes par catégorie\n", "    base_cols = ['customer_id']\n", "    rfm_cols = ['recency', 'frequency', 'monetary']\n", "    temporal_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['date', 'days', 'age', 'lifespan'])]\n", "    monetary_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['amount', 'monetary', 'value'])]\n", "    frequency_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['frequency', 'orders', 'count'])]\n", "    other_cols = [col for col in rfm_enriched.columns if col not in base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols]\n", "\n", "    # Réorganisation des colonnes\n", "    rfm_enriched = rfm_enriched[base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols + other_cols]\n", "\n", "    print(f\"\\n📊 Dataset enrichi final : {rfm_enriched.shape}\")\n", "    print(\"\\n📋 Organisation des colonnes :\")\n", "    print(f\"- Colonnes de base : {base_cols}\")\n", "    print(f\"- Métriques RFM : {rfm_cols}\")\n", "    print(f\"- Features temporelles : {temporal_cols}\")\n", "    print(f\"- Features monétaires : {monetary_cols}\")\n", "    print(f\"- Features de fréquence : {frequency_cols}\")\n", "    print(f\"- Autres features : {other_cols}\")\n", "\n", "    # Affichage des statistiques descriptives\n", "    print(\"\\n📈 Statistiques descriptives :\")\n", "    display(rfm_enriched.describe())\n", "\n", "    # Vérification des valeurs manquantes\n", "    missing_values = rfm_enriched.isnull().sum()\n", "    if missing_values.any():\n", "        print(\"\\n⚠️ Valeurs manquantes par colonne :\")\n", "        print(missing_values[missing_values > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 Taux de retour et indicateurs avancés (si applicable)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des indicateurs avancés...\n", "📋 Colonnes disponibles :\n", "['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "📅 Calcul de la saisonnalité des achats...\n", "\n", "🔍 Vérification des données :\n", "R<PERSON><PERSON> moyenne : 289.9 jours\n", "Récence médiane : 271.0 jours\n", "Récence max : 772.0 jours\n", "\n", "📊 Statistiques de période d'activité :\n", "- <PERSON><PERSON><PERSON><PERSON> moy<PERSON> : 289.9 jours\n", "- Période médiane : 271.0 jours\n", "- Période max : 772.0 jours\n", "\n", "📊 Distribution des catégories d'activité :\n", "- Long (180-365j): 41118 clients (41.3%)\n", "- Très long (>365j): 29898 clients (30.1%)\n", "- Moyen (90-180j): 18636 clients (18.7%)\n", "- Court (30-90j): 9780 clients (9.8%)\n", "- Très court (<30j): 7 clients (0.0%)\n", "\n", "📊 Statistiques de fréquence d'achat :\n", "- <PERSON><PERSON><PERSON> moyenne : 0.005 achats/jour\n", "- <PERSON><PERSON><PERSON> médiane : 0.004 achats/jour\n", "\n", "📊 Features avancées calculées pour 99,441 clients\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>activity_period_days</th>\n", "      <th>activity_period_months</th>\n", "      <th>activity_period_quarters</th>\n", "      <th>first_order_month</th>\n", "      <th>first_order_quarter</th>\n", "      <th>first_order_weekday</th>\n", "      <th>purchase_frequency</th>\n", "      <th>activity_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>337</td>\n", "      <td>11.0710</td>\n", "      <td>3.6907</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0.0030</td>\n", "      <td>Long (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>458</td>\n", "      <td>15.0460</td>\n", "      <td>5.0159</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>0.0022</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>596</td>\n", "      <td>19.5795</td>\n", "      <td>6.5272</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.0017</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>427</td>\n", "      <td>14.0276</td>\n", "      <td>4.6764</td>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.0023</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>198</td>\n", "      <td>6.5046</td>\n", "      <td>2.1684</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0051</td>\n", "      <td>Long (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  activity_period_days  activity_period_months  activity_period_quarters  first_order_month  first_order_quarter  first_order_weekday  purchase_frequency  activity_category\n", "0  00012a2ce6f8dcda20d059ce98491703                   337                 11.0710                    3.6907                 11                    4                    1              0.0030    Long (180-365j)\n", "1  000161a058600d5901f007fab4c27140                   458                 15.0460                    5.0159                  7                    3                    6              0.0022  Très long (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079                   596                 19.5795                    6.5272                  2                    1                    1              0.0017  Très long (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5                   427                 14.0276                    4.6764                  8                    3                    2              0.0023  Très long (>365j)\n", "4  000379cdec625522490c315e70c7a9fb                   198                  6.5046                    2.1684                  4                    2                    0              0.0051    Long (180-365j)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul d'indicateurs avancés : taux de retour et saisonnalité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des indicateurs avancés...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    advanced_features = pd.DataFrame()\n", "\n", "    # Saisonnalité des achats\n", "    print(\"\\n📅 Calcul de la saisonnalité des achats...\")\n", "\n", "    # Utilisation des dates disponibles\n", "    if 'first_order_date' in df_clean.columns and 'recency' in df_clean.columns:\n", "        seasonal_patterns = df_clean.copy()\n", "\n", "        # Vérification des données\n", "        print(\"\\n🔍 Vérification des données :\")\n", "        print(f\"Récence moyenne : {seasonal_patterns['recency'].mean():.1f} jours\")\n", "        print(f\"Récence médiane : {seasonal_patterns['recency'].median():.1f} jours\")\n", "        print(f\"Récence max : {seasonal_patterns['recency'].max():.1f} jours\")\n", "\n", "        # Calcul des indicateurs temporels\n", "        temporal_features = pd.DataFrame()\n", "        temporal_features['customer_id'] = seasonal_patterns['customer_id']\n", "\n", "        # Période d'activité basée sur la récence\n", "        temporal_features['activity_period_days'] = seasonal_patterns['recency']\n", "        temporal_features['activity_period_months'] = temporal_features['activity_period_days'] / 30.44\n", "        temporal_features['activity_period_quarters'] = temporal_features['activity_period_days'] / 91.31\n", "\n", "        # Saisonnalité\n", "        temporal_features['first_order_month'] = seasonal_patterns['first_order_date'].dt.month\n", "        temporal_features['first_order_quarter'] = seasonal_patterns['first_order_date'].dt.quarter\n", "        temporal_features['first_order_weekday'] = seasonal_patterns['first_order_date'].dt.dayofweek\n", "\n", "        # <PERSON><PERSON><PERSON> d'achat\n", "        if 'frequency' in seasonal_patterns.columns:\n", "            temporal_features['purchase_frequency'] = (\n", "                seasonal_patterns['frequency'] / temporal_features['activity_period_days']\n", "            ).<PERSON>na(0)\n", "            temporal_features['purchase_frequency'] = temporal_features['purchase_frequency'].replace([np.inf, -np.inf], 0)\n", "\n", "        # Catégorisation de la période d'activité\n", "        temporal_features['activity_category'] = pd.cut(\n", "            temporal_features['activity_period_days'],\n", "            bins=[0, 30, 90, 180, 365, float('inf')],\n", "            labels=['Très court (<30j)', 'Court (30-90j)', '<PERSON><PERSON><PERSON> (90-180j)',\n", "                   'Long (180-365j)', 'Tr<PERSON> long (>365j)']\n", "        )\n", "\n", "        advanced_features = temporal_features\n", "\n", "        # Affichage des statistiques\n", "        print(\"\\n📊 Statistiques de période d'activité :\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> moyenne : {temporal_features['activity_period_days'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> médiane : {temporal_features['activity_period_days'].median():.1f} jours\")\n", "        print(f\"- Période max : {temporal_features['activity_period_days'].max():.1f} jours\")\n", "\n", "        print(\"\\n📊 Distribution des catégories d'activité :\")\n", "        activity_dist = temporal_features['activity_category'].value_counts()\n", "        for category, count in activity_dist.items():\n", "            pct = (count / len(temporal_features)) * 100\n", "            print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "        if 'purchase_frequency' in temporal_features.columns:\n", "            print(\"\\n📊 Statistiques de fréquence d'achat :\")\n", "            print(f\"- <PERSON><PERSON><PERSON> moyenne : {temporal_features['purchase_frequency'].mean():.3f} achats/jour\")\n", "            print(f\"- <PERSON><PERSON><PERSON> médiane : {temporal_features['purchase_frequency'].median():.3f} achats/jour\")\n", "\n", "    else:\n", "        print(\"⚠️ Colonnes nécessaires manquantes pour l'analyse temporelle\")\n", "\n", "    print(f\"\\n📊 Features avancées calculées pour {len(advanced_features):,} clients\")\n", "    display(advanced_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation des variables\n", "\n", "### 4.1 Sélection des variables pour clustering"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Sélection des variables pour clustering...\n", "📊 Variables sélectionnées pour clustering : 24\n", "📋 Variables : ['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'monetary', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'frequency', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "🔍 Valeurs manquantes par variable :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n", "\n", "📈 Statistiques descriptives :\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/pandas/core/nanops.py:1016: RuntimeWarning: invalid value encountered in subtract\n", "  sqr = _ensure_numeric((avg - values) ** 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>monetary</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>recency_days</th>\n", "      <th>monetary</th>\n", "      <th>monetary</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_total</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std_dev</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>amount_cv_coef</th>\n", "      <th>amount_range</th>\n", "      <th>order_value_mean</th>\n", "      <th>frequency</th>\n", "      <th>frequency</th>\n", "      <th>total_orders</th>\n", "      <th>purchase_frequency</th>\n", "      <th>order_count</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>290.9002</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0013</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>167.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0025</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>272.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0037</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>401.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0060</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>773.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency  frequency   monetary   monetary  customer_lifespan_days  days_since_first_order  recency_days   monetary   monetary  total_amount  amount_std  avg_order_value  amount_cv  amount_total  avg_amount  amount_std_dev  min_amount  max_amount  amount_cv_coef  amount_range  order_value_mean  frequency  frequency  total_orders  purchase_frequency  order_count  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000 99441.0000 99441.0000              99441.0000              99441.0000    99441.0000 99441.0000 99441.0000    99441.0000      0.0000       99441.0000 99441.0000    99441.0000  99441.0000          0.0000  99441.0000  99441.0000      99441.0000    99441.0000        99441.0000 99441.0000 99441.0000    99441.0000          99441.0000   99441.0000     99441.0000\n", "mean    289.9002     1.0000     1.0000   137.3577   137.3577                  0.0000                290.9002      289.9002   137.3577   137.3577      137.3577         NaN         137.3577     0.0000      137.3577    137.3577             NaN    137.3577    137.3577          0.0000        0.0000          137.3577     1.0000     1.0000        1.0000                 inf       1.0000       137.3577\n", "std     153.6673     0.0000     0.0000   209.8703   209.8703                  0.0000                153.6673      153.6673   209.8703   209.8703      209.8703         NaN         209.8703     0.0000      209.8703    209.8703             NaN    209.8703    209.8703          0.0000        0.0000          209.8703     0.0000     0.0000        0.0000                 NaN       0.0000       209.8703\n", "min       0.0000     1.0000     1.0000     0.8500     0.8500                  0.0000                  1.0000        0.0000     0.8500     0.8500        0.8500         NaN           0.8500     0.0000        0.8500      0.8500             NaN      0.8500      0.8500          0.0000        0.0000            0.8500     1.0000     1.0000        1.0000              0.0013       1.0000         0.8500\n", "25%     166.0000     1.0000     1.0000    45.9900    45.9900                  0.0000                167.0000      166.0000    45.9900    45.9900       45.9900         NaN          45.9900     0.0000       45.9900     45.9900             NaN     45.9900     45.9900          0.0000        0.0000           45.9900     1.0000     1.0000        1.0000              0.0025       1.0000        45.9900\n", "50%     271.0000     1.0000     1.0000    86.9000    86.9000                  0.0000                272.0000      271.0000    86.9000    86.9000       86.9000         NaN          86.9000     0.0000       86.9000     86.9000             NaN     86.9000     86.9000          0.0000        0.0000           86.9000     1.0000     1.0000        1.0000              0.0037       1.0000        86.9000\n", "75%     400.0000     1.0000     1.0000   149.9000   149.9000                  0.0000                401.0000      400.0000   149.9000   149.9000      149.9000         NaN         149.9000     0.0000      149.9000    149.9000             NaN    149.9000    149.9000          0.0000        0.0000          149.9000     1.0000     1.0000        1.0000              0.0060       1.0000       149.9000\n", "max     772.0000     1.0000     1.0000 13440.0000 13440.0000                  0.0000                773.0000      772.0000 13440.0000 13440.0000    13440.0000         NaN       13440.0000     0.0000    13440.0000  13440.0000             NaN  13440.0000  13440.0000          0.0000        0.0000        13440.0000     1.0000     1.0000        1.0000                 inf       1.0000     13440.0000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sélection des variables pour le clustering\n", "if 'rfm_enriched' in locals():\n", "    print(\"🔄 Sélection des variables pour clustering...\")\n", "\n", "    # Exclusion de customer_id et autres identifiants\n", "    exclude_cols = ['customer_id', 'first_order', 'last_order']\n", "\n", "    # Sélection automatique des colonnes numériques\n", "    numeric_cols = rfm_enriched.select_dtypes(include=[np.number]).columns.tolist()\n", "    clustering_features = [col for col in numeric_cols if col not in exclude_cols]\n", "\n", "    print(f\"📊 Variables sélectionnées pour clustering : {len(clustering_features)}\")\n", "    print(f\"📋 Variables : {clustering_features}\")\n", "\n", "    # Création du dataset pour clustering\n", "    X = rfm_enriched[clustering_features].copy()\n", "\n", "    # Vérification des valeurs manquantes\n", "    print(f\"\\n🔍 Valeurs manquantes par variable :\")\n", "    missing_counts = X.isnull().sum()\n", "    if missing_counts.sum() > 0:\n", "        print(missing_counts[missing_counts > 0])\n", "    else:\n", "        print(\"✅ Aucune valeur manquante\")\n", "\n", "    # Statistiques descriptives\n", "    print(f\"\\n📈 Statistiques descriptives :\")\n", "    display(X.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Traitement des valeurs manquantes avant normalisation"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Traitement des valeurs manquantes et infinies...\n", "\n", "🧹 Nettoyage des colonnes en double...\n", "\n", "📊 Colonnes avant imputation :\n", "Nombre de colonnes : 22\n", "Liste des colonnes :\n", "['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "🔄 Imputation des valeurs manquantes avec la médiane...\n", "\n", "📊 Types de données avant conversion :\n", "recency                     int64\n", "frequency                   int64\n", "monetary                  float64\n", "customer_lifespan_days      int64\n", "days_since_first_order      int64\n", "recency_days                int64\n", "total_amount              float64\n", "amount_std                float64\n", "avg_order_value           float64\n", "amount_cv                 float64\n", "amount_total              float64\n", "avg_amount                float64\n", "amount_std_dev            float64\n", "min_amount                float64\n", "max_amount                float64\n", "amount_cv_coef            float64\n", "amount_range              float64\n", "order_value_mean          float64\n", "total_orders                int64\n", "purchase_frequency        float64\n", "order_count                 int64\n", "montant_moyen             float64\n", "dtype: object\n", "\n", "📊 Valeurs manquantes par colonne :\n", "amount_std            99441\n", "amount_std_dev        99441\n", "purchase_frequency        2\n", "dtype: int64\n", "\n", "📊 Colonnes après imputation :\n", "Nombre de colonnes : 22\n", "Liste des colonnes :\n", "['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "✅ Valeurs manquantes après imputation : 198882\n", "\n", "📊 Dataset préparé pour normalisation : (99441, 22)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py:1215: RuntimeWarning: Mean of empty slice\n", "  return np.nanmean(a, axis, out=out, keepdims=keepdims)\n", "/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py:1215: RuntimeWarning: Mean of empty slice\n", "  return np.nanmean(a, axis, out=out, keepdims=keepdims)\n"]}], "source": ["# Traitement des valeurs manquantes et infinies\n", "if 'X' in locals():\n", "    print(\"🔄 Traitement des valeurs manquantes et infinies...\")\n", "\n", "    # Vérification des valeurs infinies\n", "    inf_check = np.isinf(X).sum()\n", "    if inf_check.sum() > 0:\n", "        print(f\"⚠️ Valeurs infinies détectées : {inf_check[inf_check > 0]}\")\n", "        # Remplacer les valeurs infinies par NaN\n", "        X = X.replace([np.inf, -np.inf], np.nan)\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "    duplicate_cols = X.columns[X.columns.duplicated()].tolist()\n", "    if duplicate_cols:\n", "        print(f\"Colonnes en double détectées : {duplicate_cols}\")\n", "        # Garder la première occurrence de chaque colonne\n", "        X = X.loc[:, ~X.columns.duplicated()]\n", "        print(f\"Nombre de colonnes après nettoyage : {len(X.columns)}\")\n", "\n", "    # Vérification des colonnes avant imputation\n", "    print(\"\\n📊 Colonnes avant imputation :\")\n", "    print(f\"Nombre de colonnes : {len(X.columns)}\")\n", "    print(\"Liste des colonnes :\")\n", "    print(X.columns.tolist())\n", "\n", "    # Imputation des valeurs manquantes\n", "    if <PERSON><PERSON>isnull().sum().sum() > 0:\n", "        print(\"\\n🔄 Imputation des valeurs manquantes avec la médiane...\")\n", "\n", "        # Vérification des types de données\n", "        print(\"\\n📊 Types de données avant conversion :\")\n", "        print(X.dtypes)\n", "\n", "        # Conversion explicite des colonnes en types numériques\n", "        for col in X.columns:\n", "            try:\n", "                X[col] = pd.to_numeric(X[col], errors='coerce')\n", "            except Exception as e:\n", "                print(f\"⚠️ Erreur lors de la conversion de {col}: {str(e)}\")\n", "\n", "        # Vérification des valeurs manquantes par colonne\n", "        print(\"\\n📊 Valeurs manquantes par colonne :\")\n", "        missing_values = X.isnull().sum()\n", "        print(missing_values[missing_values > 0])\n", "\n", "        # Imputation directe avec la médiane\n", "        X_imputed = X.copy()\n", "        for col in X_imputed.columns:\n", "            median_value = X_imputed[col].median()\n", "            X_imputed[col] = X_imputed[col].fillna(median_value)\n", "\n", "        # Vérification des colonnes après imputation\n", "        print(\"\\n📊 Colonnes après imputation :\")\n", "        print(f\"Nombre de colonnes : {len(X_imputed.columns)}\")\n", "        print(\"Liste des colonnes :\")\n", "        print(X_imputed.columns.tolist())\n", "\n", "        print(f\"\\n✅ Valeurs manquantes après imputation : {X_imputed.isnull().sum().sum()}\")\n", "    else:\n", "        X_imputed = X.copy()\n", "        print(\"✅ Aucune imputation nécessaire\")\n", "\n", "    print(f\"\\n📊 Dataset préparé pour normalisation : {X_imputed.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Standardisation via StandardScaler"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Standardisation des variables...\n", "\n", "📊 Vérification des valeurs manquantes avant standardisation :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n", "\n", "📊 Vérification des valeurs manquantes après standardisation :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n", "\n", "📊 Dataset normalisé : (99441, 22)\n", "\n", "📈 Vérification de la standardisation :\n", "- Mo<PERSON>nnes (doivent être ~0) : [0.0, 0.0, -0.0, 0.0, 0.0, 0.0, -0.0, 0.0, -0.0, 0.0, -0.0, -0.0, 0.0, -0.0, -0.0, 0.0, 0.0, -0.0, 0.0, 0.0, 0.0, -0.0]\n", "- Écarts-types (doivent être ~1) : [1.000005, 0.0, 1.000005, 0.0, 1.000005, 1.000005, 1.000005, 0.0, 1.000005, 0.0, 1.000005, 1.000005, 0.0, 1.000005, 1.000005, 0.0, 0.0, 1.000005, 0.0, 1.000005, 0.0, 1.000005]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/sklearn/utils/extmath.py:1101: RuntimeWarning: invalid value encountered in divide\n", "  updated_mean = (last_sum + new_sum) / updated_sample_count\n", "/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/sklearn/utils/extmath.py:1106: RuntimeWarning: invalid value encountered in divide\n", "  T = new_sum / new_sample_count\n", "/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/sklearn/utils/extmath.py:1126: RuntimeWarning: invalid value encountered in divide\n", "  new_unnormalized_variance -= correction**2 / new_sample_count\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Figure exportée : reports/figures/2_02_normalized_distributions.png\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2000x1500 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Standardisation des variables\n", "if 'X_imputed' in locals():\n", "    print(\"🔄 Standardisation des variables...\")\n", "\n", "    # Vérification des valeurs manquantes avant standardisation\n", "    print(\"\\n📊 Vérification des valeurs manquantes avant standardisation :\")\n", "    missing_before = X_imputed.isnull().sum()\n", "    print(missing_before[missing_before > 0])\n", "\n", "    # Standardisation manuelle (moyenne=0, écart-type=1)\n", "    scaler = StandardScaler()\n", "    X_scaled = pd.DataFrame(\n", "        scaler.fit_transform(X_imputed),\n", "        columns=X_imputed.columns,\n", "        index=X_imputed.index\n", "    )\n", "\n", "    # Vérification des valeurs manquantes après standardisation\n", "    print(\"\\n📊 Vérification des valeurs manquantes après standardisation :\")\n", "    missing_after = X_scaled.isnull().sum()\n", "    print(missing_after[missing_after > 0])\n", "\n", "    # Remplacement des valeurs NaN par 0 (ou une autre stratégie appropriée)\n", "    X_scaled = X_scaled.fillna(0)\n", "\n", "    print(f\"\\n📊 Dataset normalisé : {X_scaled.shape}\")\n", "    print(f\"\\n📈 Vérification de la standardisation :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> (doivent être ~0) : {X_scaled.mean().round(6).tolist()}\")\n", "    print(f\"- Écarts-types (doivent être ~1) : {X_scaled.std().round(6).tolist()}\")\n", "\n", "    # Visualisation des distributions après normalisation\n", "    n_features = min(12, len(X_scaled.columns))\n", "    n_rows = (n_features + 3) // 4\n", "\n", "    fig, axes = plt.subplots(n_rows, 4, figsize=(20, 5*n_rows))\n", "    if n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "    axes = axes.ravel()\n", "\n", "    for i, col in enumerate(X_scaled.columns[:n_features]):\n", "        # Vérification des valeurs avant tracé\n", "        values = X_scaled[col].dropna()\n", "        if len(values) > 0:\n", "            axes[i].hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "            axes[i].axvline(0, color='red', linestyle='--', alpha=0.7, label='Moyenne=0')\n", "            axes[i].grid(True, alpha=0.3)\n", "            axes[i].legend()\n", "        else:\n", "            axes[i].text(0.5, 0.5, f'Pas de données valides\\npour {col}',\n", "                        horizontalalignment='center', verticalalignment='center')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "\n", "    # Masquer les axes non utilisés\n", "    for i in range(n_features, len(axes)):\n", "        axes[i].set_visible(False)\n", "\n", "    plt.suptitle('Distributions des Variables Normalisées', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=2, base_name=\"normalized_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Export et backup des variables d'origine"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Préparation des datasets finaux...\n", "📊 Dataset final pour clustering : (99441, 22)\n", "📊 Dataset complet avec IDs : (99441, 23)\n", "📊 Dataset enrichi original : (99441, 50)\n", "\n", "📋 Résumé des features créées :\n", "- RFM de base: 14 features\n", "- Temporelles: 4 features\n", "- Variabilité: 14 features\n", "\n", "✅ Total: 50 variables dans le dataset complet\n", "✅ Variables pour clustering: 22\n"]}], "source": ["# Export et backup des variables d'origine et normalisées\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Préparation des datasets finaux...\")\n", "\n", "    # Sauvegarde du dataset complet avec versions originales et normalisées\n", "    rfm_final = rfm_enriched.copy()\n", "\n", "    # Ajout des versions normalisées avec suffixe '_scaled'\n", "    for col in X_scaled.columns:\n", "        if col in rfm_final.columns:\n", "            rfm_final[f'{col}_scaled'] = X_scaled[col]\n", "\n", "    # Dataset pour clustering avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id'].values\n", "\n", "    # Statistiques finales\n", "    print(f\"📊 Dataset final pour clustering : {X_scaled.shape}\")\n", "    print(f\"📊 Dataset complet avec IDs : {X_scaled_with_id.shape}\")\n", "    print(f\"📊 Dataset enrichi original : {rfm_final.shape}\")\n", "\n", "    # Résumé des features créées\n", "    print(f\"\\n📋 Résumé des features créées :\")\n", "    feature_types = {\n", "        'RFM de base': [col for col in rfm_final.columns if any(x in col.lower() for x in ['recency', 'frequency', 'monetary', 'recence', 'frequence', 'montant'])],\n", "        'Temporelles': [col for col in rfm_final.columns if any(x in col.lower() for x in ['lifespan', 'days_since', 'interval', 'between'])],\n", "        'Variabilité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['std', 'cv', 'min', 'max', 'range'])],\n", "        'Diversité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['unique', 'categories', 'diversity'])],\n", "        'Avancées': [col for col in rfm_final.columns if any(x in col.lower() for x in ['cancellation', 'delivery', 'quarter', 'seasonal'])],\n", "        'Ratios': [col for col in rfm_final.columns if any(x in col.lower() for x in ['per_', 'ratio', '_per_'])]\n", "    }\n", "\n", "    for feature_type, features in feature_types.items():\n", "        if features:\n", "            print(f\"- {feature_type}: {len(features)} features\")\n", "\n", "    print(f\"\\n✅ Total: {len(rfm_final.columns)} variables dans le dataset complet\")\n", "    print(f\"✅ Variables pour clustering: {len(X_scaled.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Export du jeu final prêt à clusteriser"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Export des datasets pour le clustering...\n", "✅ Dataset clustering sauvegardé : data/processed/2_01_features_scaled_clustering.csv\n", "✅ Dataset avec IDs sauvegardé : data/processed/2_02_features_scaled_with_ids.csv\n", "✅ Dataset complet final sauvegardé : data/processed/2_03_rfm_enriched_complete.csv\n", "✅ Scaler sauvegardé : data/processed/2_04_scaler.joblib\n", "\n", "📊 Résumé des exports :\n", "- Dataset clustering : (99441, 22) - data/processed/2_01_features_scaled_clustering.csv\n", "- Dataset avec IDs : (99441, 23) - data/processed/2_02_features_scaled_with_ids.csv\n", "- Dataset complet final : (99441, 50) - data/processed/2_03_rfm_enriched_complete.csv\n"]}], "source": ["# Export des datasets pour le clustering\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Export des datasets pour le clustering...\")\n", "\n", "    # Création du dossier de sortie\n", "    os.makedirs('data/processed', exist_ok=True)\n", "\n", "    # Dataset normalisé pour clustering (sans customer_id)\n", "    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'\n", "    X_scaled.to_csv(clustering_data_path, index=False)\n", "    print(f\"✅ Dataset clustering sauvegardé : {clustering_data_path}\")\n", "\n", "    # Dataset avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id']\n", "\n", "    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "    X_scaled_with_id.to_csv(traceability_path, index=False)\n", "    print(f\"✅ Dataset avec IDs sauvegardé : {traceability_path}\")\n", "\n", "    # Dataset complet enrichi avec versions originales et normalisées\n", "    if 'rfm_final' in locals():\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_final.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset complet final sauvegardé : {complete_path}\")\n", "    else:\n", "        # Fallback vers rfm_enriched si rfm_final n'existe pas\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_enriched.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset enrichi sauvegardé : {complete_path}\")\n", "\n", "    # Sauvegarde du scaler pour usage futur\n", "    if 'scaler' in locals():\n", "        import joblib\n", "        scaler_path = 'data/processed/2_04_scaler.joblib'\n", "        joblib.dump(scaler, scaler_path)\n", "        print(f\"✅ Scaler sauvegardé : {scaler_path}\")\n", "\n", "    print(f\"\\n📊 Résumé des exports :\")\n", "    print(f\"- Dataset clustering : {X_scaled.shape} - {clustering_data_path}\")\n", "    if 'X_scaled_with_id' in locals():\n", "        print(f\"- Dataset avec IDs : {X_scaled_with_id.shape} - {traceability_path}\")\n", "    if 'rfm_final' in locals():\n", "        print(f\"- Dataset complet final : {rfm_final.shape} - {complete_path}\")\n", "    else:\n", "        print(f\"- Dataset enrichi : {rfm_enriched.shape} - {complete_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Export de la liste des variables utilisées"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📝 Export de la configuration des features...\n", "✅ Configuration sauvegardée : data/processed/2_05_feature_engineering_config.json\n", "📊 Variables finales pour clustering : 22\n", "👥 Nombre de clients : 99,441\n"]}], "source": ["# Export de la configuration des features\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"📝 Export de la configuration des features...\")\n", "\n", "    # Création de la configuration\n", "    feature_config = {\n", "        'notebook': '2_feature_engineering',\n", "        'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'reference_date': reference_date.strftime('%Y-%m-%d') if 'reference_date' in locals() else None,\n", "        'clustering_features': list(X_scaled.columns),\n", "        'original_shape': list(rfm_enriched.shape),\n", "        'final_shape': list(X_scaled.shape),\n", "        'normalization_method': 'StandardScaler',\n", "        'imputation_strategy': 'median',\n", "        'n_customers': len(rfm_enriched),\n", "        'n_features': len(X_scaled.columns),\n", "        'feature_descriptions': {\n", "            'recency': '<PERSON><PERSON> depuis le dernier achat',\n", "            'frequency': 'Nombre total d\\'achats',\n", "            'monetary': '<PERSON><PERSON> des achats',\n", "            'customer_lifespan_days': '<PERSON><PERSON>e entre premier et dernier achat',\n", "            'days_since_first_order': 'Jo<PERSON> depuis le premier achat',\n", "            'avg_days_between_orders': '<PERSON><PERSON><PERSON> moy<PERSON> entre commandes',\n", "            'order_std': 'Écart-type des montants',\n", "            'order_cv': 'Coefficient de variation des montants',\n", "            'monetary_per_frequency': 'Montant total / fréquence',\n", "            'frequency_per_day': 'Fréquence normalisée par jour'\n", "        },\n", "        'files_exported': {\n", "            'clustering_data': '2_01_features_scaled_clustering.csv',\n", "            'data_with_ids': '2_02_features_scaled_with_ids.csv',\n", "            'complete_data': '2_03_rfm_enriched_complete.csv',\n", "            'scaler': '2_04_scaler.joblib'\n", "        }\n", "    }\n", "\n", "    # Sauvegarde de la configuration\n", "    config_path = 'data/processed/2_05_feature_engineering_config.json'\n", "    with open(config_path, 'w', encoding='utf-8') as f:\n", "        json.dump(feature_config, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "    print(f\"✅ Configuration sauvegardée : {config_path}\")\n", "    print(f\"📊 Variables finales pour clustering : {len(X_scaled.columns)}\")\n", "    print(f\"👥 Nombre de clients : {len(rfm_enriched):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ **Section 1 :** Chargement des données nettoyées du Notebook 1 avec modules utils\n", "- ✅ **Section 2 :** Calcul des variables RFM (Ré<PERSON>, Fréquence, Montant) avec visualisations\n", "- ✅ **Section 3 :** Enrichissement comportemental complet :\n", "  - 3.1 : Ancienneté client et features temporelles\n", "  - 3.2 : <PERSON><PERSON><PERSON> entre commandes et features transactionnelles\n", "  - 3.3 : Diversité des achats et variabilité des montants\n", "  - 3.4 : Consolidation de toutes les features\n", "  - 3.5 : <PERSON><PERSON> de retour et indicateurs avancés (saisonnalité)\n", "- ✅ **Section 4 :** Normalisation et préparation pour clustering :\n", "  - 4.1 : Sélection automatique des variables\n", "  - 4.2 : Traitement des valeurs manquantes et infinies\n", "  - 4.3 : Standardisation avec visualisations\n", "  - 4.4 : Export et backup des versions originales et normalisées\n", "- ✅ **Section 5 :** Sauvegarde complète avec convention de nommage du projet\n", "\n", "### Variables créées pour la segmentation\n", "- **Variables RFM classiques :** récence, fréquence, montant total/moyen\n", "- **Variables temporelles :** ancie<PERSON><PERSON>, d<PERSON><PERSON>s entre commandes, diversité temporelle\n", "- **Variables de variabilité :** écart-type, coefficient de variation, min/max/range\n", "- **Variables de diversité :** catégories uniques, variabilité des achats\n", "- **Variables avancées :** taux d'annulation, livraison, saisonnalité\n", "- **Variables de ratio :** montant/fréquence, fréquence/jour\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 3 :** Clustering et segmentation avec les variables préparées\n", "\n", "---\n", "\n", "**Dataset enrichi et normalisé prêt pour la segmentation !**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}