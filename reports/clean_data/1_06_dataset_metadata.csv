key,value
dataset_name,cleaned_customer_data
shape,"(99441, 14)"
columns,"['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']"
dtypes,"{'customer_id': dtype('O'), 'frequency': dtype('int64'), 'first_order_date': dtype('<M8[ns]'), 'last_order_date': dtype('<M8[ns]'), 'recency': dtype('int64'), 'customer_state': dtype('O'), 'customer_city': dtype('O'), 'monetary': dtype('float64'), 'value_segment': CategoricalDtype(categories=['Faible', 'Moyen', 'Elevé'], ordered=True, categories_dtype=object), 'frequency_outlier': dtype('bool'), 'recency_outlier': dtype('bool'), 'monetary_outlier': dtype('bool'), 'customer_age_days': dtype('int64'), 'customer_age_category': CategoricalDtype(categories=['Nouveau (<30j)', 'Récent (30-90j)', 'Etabli (90-180j)', 'Ancien (180-365j)', 'Très ancien (>365j)'], ordered=True, categories_dtype=object)}"
processing_steps,"['Chargement depuis SQLite', 'Création métriques RFM', 'Jointure tables clients', 'Nettoyage valeurs manquantes', 'Analyse exploratoire complète']"
